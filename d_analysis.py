import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import cv2
from pathlib import Path
import glob
import re
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import seaborn as sns

def load_matched_data(base_path, frame_subdir='frames', polar_subdir='polar'):
    """
    Load matched RGB frames and polar CSV data.
    
    Parameters:
    base_path (str): Base recording directory path
    frame_subdir (str): Subdirectory containing RGB frames
    polar_subdir (str): Subdirectory containing polar CSV files
    
    Returns:
    dict: Dictionary with frame numbers as keys and tuples (image_path, csv_path) as values
    """
    base_path = Path(base_path)
    frame_dir = base_path / frame_subdir
    polar_dir = base_path / polar_subdir
    
    # Get all image files
    image_files = list(frame_dir.glob("*.jpg")) + list(frame_dir.glob("*.png"))
    matched_data = {}
    
    for img_path in image_files:
        # Extract frame number from image filename
        frame_match = re.search(r'frame_(\d+)', img_path.stem)
        if frame_match:
            frame_num = frame_match.group(1)
            
            # Look for corresponding polar CSV file
            polar_file = polar_dir / f"polar_{frame_num}.csv"
            
            if polar_file.exists():
                matched_data[int(frame_num)] = {
                    'image_path': img_path,
                    'csv_path': polar_file,
                    'frame_num': frame_num
                }
            else:
                print(f"Warning: No polar data found for frame {frame_num}")
    
    print(f"Found {len(matched_data)} matched frame pairs")
    return matched_data

def pixel_to_image_coords(pixel_x, image_width=1920, image_height=1080):
    """
    Convert pixel_x coordinate to image coordinates.
    Assuming pixel_x represents horizontal position and yaw represents the angle.
    
    Parameters:
    pixel_x: X coordinate from polar data
    image_width: Width of the image (1920)
    image_height: Height of the image (1080)
    
    Returns:
    tuple: (x, y) coordinates in image space
    """
    # Map pixel_x to image width - this assumes pixel_x is already in pixel coordinates
    # If pixel_x is normalized or in a different scale, adjust accordingly
    x = int(pixel_x * image_width / image_width)  # This may need adjustment based on your data
    
    # For now, we'll use a fixed y position or derive it from yaw angle
    # You might need to adjust this based on your specific sensor setup
    y = image_height // 2  # Middle of the image as default
    
    return x, y

def create_distance_overlay(image_path, csv_path, output_path=None, 
                          colormap='viridis', point_size=3, show_colorbar=True):
    """
    Create an overlay of distance data on RGB frame.
    
    Parameters:
    image_path (str): Path to RGB image
    csv_path (str): Path to corresponding CSV file
    output_path (str): Path to save the overlay image
    colormap (str): Matplotlib colormap name
    point_size (int): Size of overlay points
    show_colorbar (bool): Whether to show colorbar
    
    Returns:
    matplotlib figure
    """
    # Load image
    image = cv2.imread(str(image_path))
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB
    
    # Load polar data
    df = pd.read_csv(csv_path)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(16, 9))
    
    # Display image
    ax.imshow(image)
    
    # Prepare distance data for coloring
    distances = df['distance_meters'].values
    valid_mask = ~np.isnan(distances)
    
    if valid_mask.sum() > 0:
        # Normalize distances for coloring
        min_dist = distances[valid_mask].min()
        max_dist = distances[valid_mask].max()
        
        # Create scatter plot overlay
        # Map yaw angles to x coordinates and use a fixed or derived y coordinate
        image_height, image_width = image.shape[:2]
        
        # Convert yaw angles to x coordinates (assuming -36.5 to 36.5 degree range maps to full width)
        yaw_range = df['yaw_degrees'].max() - df['yaw_degrees'].min()
        yaw_min = df['yaw_degrees'].min()
        
        # Map yaw to x coordinates
        x_coords = ((df['yaw_degrees'] - yaw_min) / yaw_range) * image_width
        
        # For y coordinates, we can use multiple strategies:
        # Strategy 1: Fixed height based on distance (closer = lower in image)
        # Strategy 2: Use pixel_x as y coordinate if it represents vertical position
        # Strategy 3: Distribute points vertically
        
        # Strategy 1: Map distance to vertical position (closer objects appear lower)
        y_coords = image_height * 0.8 - (distances - min_dist) / (max_dist - min_dist) * (image_height * 0.6)
        
        # Create the scatter plot
        scatter = ax.scatter(x_coords[valid_mask], y_coords[valid_mask], 
                           c=distances[valid_mask], cmap=colormap, 
                           s=point_size**2, alpha=0.8, edgecolors='white', linewidth=0.5)
        
        if show_colorbar:
            cbar = plt.colorbar(scatter, ax=ax, fraction=0.046, pad=0.04)
            cbar.set_label('Distance (meters)', fontsize=12)
    
    # Add title and labels
    ax.set_title(f'Distance Overlay - Frame {Path(csv_path).stem}', fontsize=14, pad=20)
    ax.set_xlabel('Image Width (pixels)', fontsize=12)
    ax.set_ylabel('Image Height (pixels)', fontsize=12)
    
    # Remove axis ticks for cleaner look
    ax.set_xticks([])
    ax.set_yticks([])
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
    
    return fig

def create_polar_visualization(csv_path, output_path=None, image_size=(10, 8)):
    """
    Create a polar plot of the distance data.
    
    Parameters:
    csv_path (str): Path to CSV file
    output_path (str): Path to save the plot
    image_size (tuple): Figure size
    
    Returns:
    matplotlib figure
    """
    df = pd.read_csv(csv_path)
    
    # Create polar plot
    fig, ax = plt.subplots(figsize=image_size, subplot_kw=dict(projection='polar'))
    
    # Convert yaw degrees to radians
    angles = np.deg2rad(df['yaw_degrees'])
    distances = df['distance_meters']
    
    # Create the polar scatter plot
    scatter = ax.scatter(angles, distances, c=distances, cmap='viridis', 
                        s=30, alpha=0.7, edgecolors='black', linewidth=0.5)
    
    # Customize the polar plot
    ax.set_theta_zero_location('N')  # 0 degrees at top
    ax.set_theta_direction(-1)  # Clockwise
    ax.set_title(f'Polar Distance View - {Path(csv_path).stem}', pad=20, fontsize=14)
    ax.set_ylim(0, distances.max() * 1.1)
    
    # Add colorbar
    cbar = plt.colorbar(scatter, ax=ax, fraction=0.046, pad=0.1)
    cbar.set_label('Distance (meters)', fontsize=12)
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
    
    return fig

def create_combined_visualization(image_path, csv_path, output_path=None):
    """
    Create a combined visualization with RGB overlay and polar plot side by side.
    
    Parameters:
    image_path (str): Path to RGB image
    csv_path (str): Path to corresponding CSV file
    output_path (str): Path to save the combined visualization
    
    Returns:
    matplotlib figure
    """
    # Load image and data
    image = cv2.imread(str(image_path))
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    df = pd.read_csv(csv_path)
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 10))
    
    # RGB overlay subplot
    ax1 = plt.subplot2grid((2, 3), (0, 0), colspan=2, rowspan=2)
    
    # Display image
    ax1.imshow(image)
    
    # Add distance overlay
    distances = df['distance_meters'].values
    valid_mask = ~np.isnan(distances)
    
    if valid_mask.sum() > 0:
        image_height, image_width = image.shape[:2]
        
        # Map yaw to x coordinates
        yaw_range = df['yaw_degrees'].max() - df['yaw_degrees'].min()
        yaw_min = df['yaw_degrees'].min()
        x_coords = ((df['yaw_degrees'] - yaw_min) / yaw_range) * image_width
        
        # Map distance to y coordinates
        min_dist = distances[valid_mask].min()
        max_dist = distances[valid_mask].max()
        y_coords = image_height * 0.8 - (distances - min_dist) / (max_dist - min_dist) * (image_height * 0.6)
        
        scatter1 = ax1.scatter(x_coords[valid_mask], y_coords[valid_mask], 
                             c=distances[valid_mask], cmap='plasma', 
                             s=25, alpha=0.8, edgecolors='white', linewidth=0.5)
    
    ax1.set_title('RGB Frame with Distance Overlay', fontsize=14)
    ax1.set_xticks([])
    ax1.set_yticks([])
    
    # Polar plot subplot
    ax2 = plt.subplot2grid((2, 3), (0, 2), projection='polar')
    
    angles = np.deg2rad(df['yaw_degrees'])
    scatter2 = ax2.scatter(angles, distances, c=distances, cmap='plasma', 
                          s=30, alpha=0.7, edgecolors='black', linewidth=0.5)
    
    ax2.set_theta_zero_location('N')
    ax2.set_theta_direction(-1)
    ax2.set_title('Polar View', pad=20, fontsize=14)
    ax2.set_ylim(0, distances.max() * 1.1)
    
    # Distance histogram subplot
    ax3 = plt.subplot2grid((2, 3), (1, 2))
    ax3.hist(distances[valid_mask], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.set_xlabel('Distance (m)')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Distance Distribution')
    ax3.grid(True, alpha=0.3)
    
    # Add a shared colorbar
    if valid_mask.sum() > 0:
        cbar = fig.colorbar(scatter1, ax=[ax1, ax2], fraction=0.046, pad=0.04)
        cbar.set_label('Distance (meters)', fontsize=12)
    
    plt.suptitle(f'Frame Analysis: {Path(csv_path).stem}', fontsize=16, y=0.95)
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
    
    return fig

def process_all_frames(matched_data, output_dir="output_visualizations", 
                      create_individual=True, create_combined=True):
    """
    Process all matched frames and create visualizations.
    
    Parameters:
    matched_data (dict): Dictionary from load_matched_data
    output_dir (str): Directory to save output images
    create_individual (bool): Whether to create individual overlay images
    create_combined (bool): Whether to create combined visualizations
    
    Returns:
    None
    """
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    if create_individual:
        overlay_dir = output_path / "overlays"
        overlay_dir.mkdir(exist_ok=True)
    
    if create_combined:
        combined_dir = output_path / "combined"
        combined_dir.mkdir(exist_ok=True)
    
    print("Processing frames...")
    
    for frame_num in sorted(matched_data.keys()):
        data = matched_data[frame_num]
        print(f"Processing frame {frame_num:06d}...")
        
        try:
            if create_individual:
                fig1 = create_distance_overlay(
                    data['image_path'], 
                    data['csv_path'],
                    output_path=overlay_dir / f"overlay_{frame_num:06d}.png"
                )
                plt.close(fig1)
            
            if create_combined:
                fig2 = create_combined_visualization(
                    data['image_path'],
                    data['csv_path'],
                    output_path=combined_dir / f"combined_{frame_num:06d}.png"
                )
                plt.close(fig2)
                
        except Exception as e:
            print(f"Error processing frame {frame_num}: {e}")
    
    print(f"Processing complete! Check {output_dir} for results.")

def create_video_from_frames(image_dir, output_video="distance_overlay.mp4", fps=10):
    """
    Create a video from processed frame images.
    
    Parameters:
    image_dir (str): Directory containing processed images
    output_video (str): Output video filename
    fps (int): Frames per second
    
    Returns:
    None
    """
    try:
        import subprocess
        
        # Use ffmpeg to create video
        cmd = [
            'ffmpeg', '-y',  # -y to overwrite output file
            '-framerate', str(fps),
            '-pattern_type', 'glob',
            '-i', f'{image_dir}/*.png',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            output_video
        ]
        
        subprocess.run(cmd, check=True)
        print(f"Video created: {output_video}")
        
    except subprocess.CalledProcessError as e:
        print(f"Error creating video: {e}")
        print("Make sure ffmpeg is installed: sudo apt install ffmpeg")
    except ImportError:
        print("subprocess not available")

# Main execution function
def main():
    """
    Main function to run the visualization.
    """
    # Configuration - UPDATE THESE PATHS
    base_path = "/home/<USER>/recording_20250902_172406"  # Update this path
    
    # Load matched data
    print("Loading matched RGB frames and polar data...")
    matched_data = load_matched_data(base_path)
    
    if not matched_data:
        print("No matched data found. Please check your paths.")
        return
    
    # Process a single frame as example
    sample_frame = min(matched_data.keys())
    sample_data = matched_data[sample_frame]
    
    print(f"Creating example visualizations for frame {sample_frame}...")
    
    # Create individual visualizations
    fig1 = create_distance_overlay(sample_data['image_path'], sample_data['csv_path'])
    plt.show()
    
    fig2 = create_polar_visualization(sample_data['csv_path'])
    plt.show()
    
    fig3 = create_combined_visualization(sample_data['image_path'], sample_data['csv_path'])
    plt.show()
    
    # Ask user if they want to process all frames
    response = input("Do you want to process all frames? (y/n): ")
    if response.lower() == 'y':
        process_all_frames(matched_data)
        
        # Ask if they want to create a video
        video_response = input("Create video from processed frames? (y/n): ")
        if video_response.lower() == 'y':
            create_video_from_frames("output_visualizations/combined")
    
    return matched_data

if __name__ == "__main__":
    # Example usage
    try:
        matched_data = main()
    except Exception as e:
        print(f"Error during execution: {e}")
        print("Please make sure to update the base_path variable with the correct path.")