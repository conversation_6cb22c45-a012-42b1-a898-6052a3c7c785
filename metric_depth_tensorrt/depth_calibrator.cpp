#include "depth_calibrator.h"
#include <iostream>
#include <cmath>

DepthCalibrator::DepthCalibrator() 
    : initialized_(false), calibration_loaded_(false), automatic_scale_factor_(1.0f), automatic_offset_(0.0f) {
}

DepthCalibrator::~DepthCalibrator() {
}

bool DepthCalibrator::initialize(const DepthCalibrationConfig& config) {
    config_ = config;
    
    if (!config_.enabled) {
        initialized_ = true;
        return true;
    }
    
    // Load camera calibration if file is specified
    if (!config_.calibration_file.empty()) {
        if (!loadCameraCalibration(config_.calibration_file)) {
            std::cerr << "Warning: Failed to load camera calibration from: " << config_.calibration_file << std::endl;
            std::cerr << "Continuing with manual calibration parameters only." << std::endl;
        }
    }
    
    // Calculate automatic correction if enabled and calibration is loaded
    if (config_.use_focal_length_correction && calibration_loaded_) {
        calculateAutomaticCorrection();
    }
    
    initialized_ = true;
    
    std::cout << "DepthCalibrator initialized:" << std::endl;
    std::cout << "  Enabled: " << (config_.enabled ? "true" : "false") << std::endl;
    std::cout << "  Calibration file: " << config_.calibration_file << std::endl;
    std::cout << "  Use focal length correction: " << (config_.use_focal_length_correction ? "true" : "false") << std::endl;
    std::cout << "  Use manual correction: " << (config_.use_manual_correction ? "true" : "false") << std::endl;
    
    if (config_.use_manual_correction) {
        std::cout << "  Manual depth offset: " << config_.depth_offset_meters << " meters" << std::endl;
        std::cout << "  Manual depth scale: " << config_.depth_scale_factor << std::endl;
    }
    
    if (calibration_loaded_) {
        printCalibrationInfo();
    }
    
    return true;
}

bool DepthCalibrator::loadConfig(const YAML::Node& config_node) {
    try {
        if (!config_node) {
            std::cout << "No depth_calibration configuration found, using defaults (disabled)" << std::endl;
            config_.enabled = false;
            return true;
        }
        
        config_.enabled = config_node["enabled"].as<bool>(false);
        config_.calibration_file = config_node["calibration_file"].as<std::string>("calibration_data/camera_0_calib.xml");
        config_.focal_length_scale_factor = config_node["focal_length_scale_factor"].as<float>(1.0f);
        config_.depth_offset_meters = config_node["depth_offset_meters"].as<float>(0.0f);
        config_.depth_scale_factor = config_node["depth_scale_factor"].as<float>(1.0f);
        config_.use_focal_length_correction = config_node["use_focal_length_correction"].as<bool>(true);
        config_.use_manual_correction = config_node["use_manual_correction"].as<bool>(false);
        
        // Model parameters
        config_.model_fx = config_node["model_fx"].as<double>(725.0087);
        config_.model_fy = config_node["model_fy"].as<double>(725.0087);
        config_.model_width = config_node["model_width"].as<int>(1241);
        config_.model_height = config_node["model_height"].as<int>(374);
        
        return initialize(config_);
    } catch (const std::exception& e) {
        std::cerr << "Error loading depth calibration configuration: " << e.what() << std::endl;
        config_.enabled = false;
        return false;
    }
}

bool DepthCalibrator::loadCameraCalibration(const std::string& calibration_file) {
    cv::FileStorage fs(calibration_file, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        std::cerr << "Failed to open calibration file: " << calibration_file << std::endl;
        return false;
    }
    
    bool success = true;
    success &= parseCameraMatrix(fs);
    success &= parseDistortionCoefficients(fs);
    success &= parseImageSize(fs);
    
    fs.release();
    
    if (success) {
        calibration_loaded_ = true;
        std::cout << "Camera calibration loaded successfully from: " << calibration_file << std::endl;
    } else {
        std::cerr << "Failed to parse camera calibration data" << std::endl;
    }
    
    return success;
}

cv::Mat DepthCalibrator::calibrateDepth(const cv::Mat& depth_map) {
    if (!initialized_ || !config_.enabled || depth_map.empty()) {
        return depth_map.clone();
    }
    
    cv::Mat calibrated_depth = depth_map.clone();
    
    // Apply automatic focal length correction
    if (config_.use_focal_length_correction && calibration_loaded_) {
        calibrated_depth *= automatic_scale_factor_;
        calibrated_depth += automatic_offset_;
    }
    
    // Apply manual correction
    if (config_.use_manual_correction) {
        calibrated_depth *= config_.depth_scale_factor;
        calibrated_depth += config_.depth_offset_meters;
    }
    
    // Apply focal length scale factor if specified
    if (config_.focal_length_scale_factor != 1.0f) {
        calibrated_depth *= config_.focal_length_scale_factor;
    }
    
    return calibrated_depth;
}

void DepthCalibrator::calculateAutomaticCorrection() {
    if (!calibration_loaded_) {
        return;
    }
    
    // Calculate focal length ratio
    double fx_ratio = camera_intrinsics_.fx / config_.model_fx;
    double fy_ratio = camera_intrinsics_.fy / config_.model_fy;
    double avg_focal_ratio = (fx_ratio + fy_ratio) / 2.0;
    
    // For monocular depth estimation, depth is often inversely proportional to focal length
    // If our camera has higher focal length, the model will underestimate depth
    automatic_scale_factor_ = static_cast<float>(avg_focal_ratio);
    
    // Calculate resolution ratio for additional correction
    double width_ratio = static_cast<double>(camera_intrinsics_.width) / config_.model_width;
    double height_ratio = static_cast<double>(camera_intrinsics_.height) / config_.model_height;
    double avg_resolution_ratio = (width_ratio + height_ratio) / 2.0;
    
    // Additional scaling based on resolution difference
    automatic_scale_factor_ *= static_cast<float>(std::sqrt(avg_resolution_ratio));
    
    // No automatic offset for now (can be added based on empirical data)
    automatic_offset_ = 0.0f;
    
    std::cout << "Automatic correction calculated:" << std::endl;
    std::cout << "  Focal length ratio: " << avg_focal_ratio << std::endl;
    std::cout << "  Resolution ratio: " << avg_resolution_ratio << std::endl;
    std::cout << "  Automatic scale factor: " << automatic_scale_factor_ << std::endl;
}

void DepthCalibrator::printCalibrationInfo() const {
    if (!calibration_loaded_) {
        std::cout << "No camera calibration loaded" << std::endl;
        return;
    }
    
    std::cout << "Camera Calibration Info:" << std::endl;
    std::cout << "  Resolution: " << camera_intrinsics_.width << "x" << camera_intrinsics_.height << std::endl;
    std::cout << "  Focal lengths: fx=" << camera_intrinsics_.fx << ", fy=" << camera_intrinsics_.fy << std::endl;
    std::cout << "  Principal point: cx=" << camera_intrinsics_.cx << ", cy=" << camera_intrinsics_.cy << std::endl;
    
    // Calculate field of view
    double fov_x = 2.0 * std::atan(camera_intrinsics_.width / (2.0 * camera_intrinsics_.fx)) * 180.0 / M_PI;
    double fov_y = 2.0 * std::atan(camera_intrinsics_.height / (2.0 * camera_intrinsics_.fy)) * 180.0 / M_PI;
    std::cout << "  Field of view: " << fov_x << "° x " << fov_y << "°" << std::endl;
    
    std::cout << "Model Training Parameters:" << std::endl;
    std::cout << "  Resolution: " << config_.model_width << "x" << config_.model_height << std::endl;
    std::cout << "  Focal lengths: fx=" << config_.model_fx << ", fy=" << config_.model_fy << std::endl;
    
    // Calculate model FOV
    double model_fov_x = 2.0 * std::atan(config_.model_width / (2.0 * config_.model_fx)) * 180.0 / M_PI;
    double model_fov_y = 2.0 * std::atan(config_.model_height / (2.0 * config_.model_fy)) * 180.0 / M_PI;
    std::cout << "  Field of view: " << model_fov_x << "° x " << model_fov_y << "°" << std::endl;
}

float DepthCalibrator::estimateScaleFromKnownDistance(const cv::Mat& measured_depth_map, 
                                                     float known_distance_meters,
                                                     const cv::Rect& roi) {
    if (measured_depth_map.empty()) {
        return 1.0f;
    }
    
    cv::Mat region = measured_depth_map;
    if (roi.width > 0 && roi.height > 0) {
        region = measured_depth_map(roi);
    }
    
    // Calculate median depth in the region (more robust than mean)
    std::vector<float> depths;
    for (int y = 0; y < region.rows; ++y) {
        for (int x = 0; x < region.cols; ++x) {
            float depth = region.at<float>(y, x);
            if (depth > 0 && std::isfinite(depth)) {
                depths.push_back(depth);
            }
        }
    }
    
    if (depths.empty()) {
        return 1.0f;
    }
    
    std::sort(depths.begin(), depths.end());
    float median_depth = depths[depths.size() / 2];
    
    float scale_factor = known_distance_meters / median_depth;
    
    std::cout << "Scale estimation:" << std::endl;
    std::cout << "  Measured median depth: " << median_depth << " meters" << std::endl;
    std::cout << "  Known distance: " << known_distance_meters << " meters" << std::endl;
    std::cout << "  Estimated scale factor: " << scale_factor << std::endl;
    
    return scale_factor;
}

bool DepthCalibrator::parseCameraMatrix(cv::FileStorage& fs) {
    cv::Mat camera_matrix;
    fs["camera_matrix"] >> camera_matrix;
    
    if (camera_matrix.empty() || camera_matrix.rows != 3 || camera_matrix.cols != 3) {
        std::cerr << "Invalid camera matrix in calibration file" << std::endl;
        return false;
    }
    
    camera_intrinsics_.fx = camera_matrix.at<double>(0, 0);
    camera_intrinsics_.fy = camera_matrix.at<double>(1, 1);
    camera_intrinsics_.cx = camera_matrix.at<double>(0, 2);
    camera_intrinsics_.cy = camera_matrix.at<double>(1, 2);
    
    return true;
}

bool DepthCalibrator::parseDistortionCoefficients(cv::FileStorage& fs) {
    fs["distortion_coefficients"] >> camera_intrinsics_.distortion;
    return !camera_intrinsics_.distortion.empty();
}

bool DepthCalibrator::parseImageSize(cv::FileStorage& fs) {
    cv::Mat image_size;
    fs["image_size"] >> image_size;
    
    if (image_size.empty() || image_size.total() != 2) {
        std::cerr << "Invalid image size in calibration file" << std::endl;
        return false;
    }
    
    camera_intrinsics_.width = image_size.at<int>(0);
    camera_intrinsics_.height = image_size.at<int>(1);
    
    return true;
}
