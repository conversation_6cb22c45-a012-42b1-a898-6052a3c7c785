#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <yaml-cpp/yaml.h>
#include "depth_strip_processor.h"

/**
 * @brief Configuration structure for radar visualization
 */
struct RadarVisualizerConfig {
    bool enabled;                    // Enable radar visualization
    float max_radius_meters;         // Maximum radar radius in meters
    int window_width;               // Radar window width in pixels
    int window_height;              // Radar window height in pixels
    bool show_grid;                 // Show radar grid lines
    bool show_range_rings;          // Show range rings
    bool show_angle_lines;          // Show angle lines
    int grid_divisions;             // Number of grid divisions
    float point_size;               // Size of object points
    cv::Scalar background_color;    // Background color (B, G, R)
    cv::Scalar grid_color;          // Grid color (B, G, R)
    cv::Scalar range_ring_color;    // Range ring color (B, G, R)
    cv::Scalar angle_line_color;    // Angle line color (B, G, R)
    cv::Scalar object_color;        // Object point color (B, G, R)
    cv::Scalar center_color;        // Center point color (B, G, R)
    bool save_radar_images;         // Save radar images to disk
    std::string output_directory;   // Output directory for saved images
    bool show_distance_labels;      // Show distance labels on range rings
    bool show_angle_labels;         // Show angle labels
    float update_rate_hz;           // Update rate in Hz (0 = as fast as possible)
    
    // Default values
    RadarVisualizerConfig() 
        : enabled(false)
        , max_radius_meters(80.0f)
        , window_width(800)
        , window_height(800)
        , show_grid(true)
        , show_range_rings(true)
        , show_angle_lines(true)
        , grid_divisions(8)
        , point_size(3.0f)
        , background_color(cv::Scalar(0, 0, 0))        // Black
        , grid_color(cv::Scalar(50, 50, 50))           // Dark gray
        , range_ring_color(cv::Scalar(100, 100, 100))  // Gray
        , angle_line_color(cv::Scalar(80, 80, 80))     // Light gray
        , object_color(cv::Scalar(0, 255, 0))          // Green
        , center_color(cv::Scalar(0, 0, 255))          // Red
        , save_radar_images(false)
        , output_directory("radar_output")
        , show_distance_labels(true)
        , show_angle_labels(true)
        , update_rate_hz(10.0f) {}
};

/**
 * @brief Radar visualizer class
 * 
 * This class provides functionality to visualize polar coordinate data
 * (yaw, distance) on a radar-like display. Objects are shown as points
 * on the radar with configurable appearance and grid overlay.
 */
class RadarVisualizer {
public:
    RadarVisualizer();
    ~RadarVisualizer();
    
    /**
     * @brief Initialize the visualizer with configuration
     * @param config Configuration structure
     * @return true if initialization successful
     */
    bool initialize(const RadarVisualizerConfig& config);
    
    /**
     * @brief Load configuration from YAML node
     * @param config_node YAML node containing radar_visualizer configuration
     * @return true if configuration loaded successfully
     */
    bool loadConfig(const YAML::Node& config_node);
    
    /**
     * @brief Update radar display with new polar coordinate data
     * @param polar_points Vector of polar coordinate points
     * @param frame_id Optional frame ID for saved images
     */
    void update(const std::vector<PolarPoint>& polar_points, int frame_id = -1);
    
    /**
     * @brief Check if visualizer is enabled
     * @return true if enabled
     */
    bool isEnabled() const { return config_.enabled; }
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    const RadarVisualizerConfig& getConfig() const { return config_; }
    
    /**
     * @brief Show the radar window (call this in main loop)
     * @param wait_key_ms Milliseconds to wait for key press (default: 1)
     * @return Key code pressed, or -1 if no key
     */
    int show(int wait_key_ms = 1);
    
    /**
     * @brief Save current radar image to file
     * @param filename Output filename
     */
    void saveImage(const std::string& filename);

private:
    RadarVisualizerConfig config_;
    bool initialized_;
    cv::Mat radar_image_;
    cv::Point2f center_point_;
    float pixels_per_meter_;
    std::chrono::high_resolution_clock::time_point last_update_time_;
    
    /**
     * @brief Create the base radar display (grid, rings, etc.)
     */
    void createBaseRadar();
    
    /**
     * @brief Draw range rings on the radar
     */
    void drawRangeRings();
    
    /**
     * @brief Draw angle lines on the radar
     */
    void drawAngleLines();
    
    /**
     * @brief Draw grid lines on the radar
     */
    void drawGrid();
    
    /**
     * @brief Draw distance labels on range rings
     */
    void drawDistanceLabels();
    
    /**
     * @brief Draw angle labels
     */
    void drawAngleLabels();
    
    /**
     * @brief Convert polar coordinates to pixel coordinates
     * @param yaw_degrees Yaw angle in degrees
     * @param distance_meters Distance in meters
     * @return Pixel coordinates
     */
    cv::Point2f polarToPixel(float yaw_degrees, float distance_meters);
    
    /**
     * @brief Draw object points on the radar
     * @param polar_points Vector of polar coordinate points
     */
    void drawObjects(const std::vector<PolarPoint>& polar_points);
    
    /**
     * @brief Check if enough time has passed for update based on update rate
     * @return true if should update
     */
    bool shouldUpdate();
};
