#pragma once

#include <opencv2/opencv.hpp>
#include <string>
#include <yaml-cpp/yaml.h>

/**
 * @brief Camera intrinsic parameters
 */
struct CameraIntrinsics {
    double fx, fy;          // Focal lengths in pixels
    double cx, cy;          // Principal point coordinates
    int width, height;      // Image resolution
    cv::Mat distortion;     // Distortion coefficients
    
    CameraIntrinsics() : fx(0), fy(0), cx(0), cy(0), width(0), height(0) {}
};

/**
 * @brief Depth calibration configuration
 */
struct DepthCalibrationConfig {
    bool enabled;                    // Enable depth calibration
    std::string calibration_file;    // Path to camera calibration XML file
    float focal_length_scale_factor; // Manual scale factor for focal length correction
    float depth_offset_meters;       // Constant depth offset in meters
    float depth_scale_factor;        // Multiplicative depth scale factor
    bool use_focal_length_correction; // Use automatic focal length based correction
    bool use_manual_correction;      // Use manual offset and scale
    
    // Model training parameters for reference
    double model_fx, model_fy;       // Model training focal lengths
    int model_width, model_height;   // Model training resolution
    
    // Default values
    DepthCalibrationConfig()
        : enabled(false)
        , calibration_file("calibration_data/camera_0_calib.xml")
        , focal_length_scale_factor(1.0f)
        , depth_offset_meters(0.0f)
        , depth_scale_factor(1.0f)
        , use_focal_length_correction(true)
        , use_manual_correction(false)
        , model_fx(725.0087)
        , model_fy(725.0087)
        , model_width(1241)
        , model_height(374) {}
};

/**
 * @brief Depth calibrator class for correcting depth estimation errors
 * 
 * This class handles depth calibration to correct systematic errors in
 * monocular depth estimation due to camera parameter differences.
 */
class DepthCalibrator {
public:
    DepthCalibrator();
    ~DepthCalibrator();
    
    /**
     * @brief Initialize the calibrator with configuration
     * @param config Configuration structure
     * @return true if initialization successful
     */
    bool initialize(const DepthCalibrationConfig& config);
    
    /**
     * @brief Load configuration from YAML node
     * @param config_node YAML node containing depth_calibration configuration
     * @return true if configuration loaded successfully
     */
    bool loadConfig(const YAML::Node& config_node);
    
    /**
     * @brief Load camera calibration from XML file
     * @param calibration_file Path to OpenCV calibration XML file
     * @return true if loaded successfully
     */
    bool loadCameraCalibration(const std::string& calibration_file);
    
    /**
     * @brief Calibrate depth map using loaded parameters
     * @param depth_map Input depth map (CV_32F)
     * @return Calibrated depth map
     */
    cv::Mat calibrateDepth(const cv::Mat& depth_map);
    
    /**
     * @brief Check if calibrator is enabled
     * @return true if enabled
     */
    bool isEnabled() const { return config_.enabled; }
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    const DepthCalibrationConfig& getConfig() const { return config_; }
    
    /**
     * @brief Get loaded camera intrinsics
     * @return Camera intrinsics
     */
    const CameraIntrinsics& getCameraIntrinsics() const { return camera_intrinsics_; }
    
    /**
     * @brief Calculate automatic correction factors based on camera parameters
     */
    void calculateAutomaticCorrection();
    
    /**
     * @brief Print calibration information
     */
    void printCalibrationInfo() const;
    
    /**
     * @brief Estimate depth scale factor from known distance measurement
     * @param measured_depth_map Depth map containing object at known distance
     * @param known_distance_meters Known real-world distance in meters
     * @param roi Region of interest containing the object (optional)
     * @return Estimated scale factor
     */
    float estimateScaleFromKnownDistance(const cv::Mat& measured_depth_map, 
                                        float known_distance_meters,
                                        const cv::Rect& roi = cv::Rect());

private:
    DepthCalibrationConfig config_;
    CameraIntrinsics camera_intrinsics_;
    bool initialized_;
    bool calibration_loaded_;
    
    // Calculated correction factors
    float automatic_scale_factor_;
    float automatic_offset_;
    
    /**
     * @brief Parse camera matrix from OpenCV XML format
     * @param fs OpenCV FileStorage object
     * @return true if parsed successfully
     */
    bool parseCameraMatrix(cv::FileStorage& fs);
    
    /**
     * @brief Parse distortion coefficients from OpenCV XML format
     * @param fs OpenCV FileStorage object
     * @return true if parsed successfully
     */
    bool parseDistortionCoefficients(cv::FileStorage& fs);
    
    /**
     * @brief Parse image size from OpenCV XML format
     * @param fs OpenCV FileStorage object
     * @return true if parsed successfully
     */
    bool parseImageSize(cv::FileStorage& fs);
};
