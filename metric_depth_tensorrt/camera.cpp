#include "camera.h"
#include <iostream>

namespace realtime_depth_processor {
namespace camera {

CSICameraInterface::CSICameraInterface() 
    : frame_width_(0), frame_height_(0), framerate_(0), is_initialized_(false) {
}

CSICameraInterface::~CSICameraInterface() {
    release();
}

std::string CSICameraInterface::gstreamer_pipeline_optimized(int sensor_id, int width, int height, 
                                                           int framerate, int sensor_mode, int flip_method) {
    return "nvarguscamerasrc sensor-id=" + std::to_string(sensor_id) + 
           " sensor-mode=" + std::to_string(sensor_mode) + " ! " +
           "video/x-raw(memory:NVMM), width=" + std::to_string(width) + 
           ", height=" + std::to_string(height) + 
           ", format=NV12, framerate=" + std::to_string(framerate) + "/1 ! " +
           "nvvidconv flip-method=" + std::to_string(flip_method) + " ! " +
           "video/x-raw, width=" + std::to_string(width) + 
           ", height=" + std::to_string(height) + ", format=BGRx ! " +
           "videoconvert ! " +
           "video/x-raw, format=BGR ! " +
           "queue ! " +
           "appsink drop=true sync=false";
}

bool CSICameraInterface::initialize(int sensor_id, int width, int height, 
                                   int framerate, int sensor_mode, int flip_method) {
    if (is_initialized_) {
        std::cout << "Camera already initialized. Releasing previous instance." << std::endl;
        release();
    }
    
    std::string pipeline = gstreamer_pipeline_optimized(sensor_id, width, height, 
                                                       framerate, sensor_mode, flip_method);
    
    std::cout << "GStreamer pipeline: " << pipeline << std::endl;
    
    cap_.open(pipeline, cv::CAP_GSTREAMER);
    
    if (!cap_.isOpened()) {
        std::cerr << "Failed to open CSI camera with GStreamer pipeline!" << std::endl;
        std::cerr << "Make sure:" << std::endl;
        std::cerr << "1. Camera is properly connected" << std::endl;
        std::cerr << "2. GStreamer is installed with NVIDIA plugins" << std::endl;
        std::cerr << "3. User has permission to access camera device" << std::endl;
        return false;
    }
    
    // Verify the actual capture properties
    frame_width_ = static_cast<int>(cap_.get(cv::CAP_PROP_FRAME_WIDTH));
    frame_height_ = static_cast<int>(cap_.get(cv::CAP_PROP_FRAME_HEIGHT));
    framerate_ = static_cast<int>(cap_.get(cv::CAP_PROP_FPS));
    
    std::cout << "Camera initialized successfully:" << std::endl;
    std::cout << "  Resolution: " << frame_width_ << "x" << frame_height_ << std::endl;
    std::cout << "  Framerate: " << framerate_ << " fps" << std::endl;
    std::cout << "  Sensor mode: " << sensor_mode << std::endl;
    
    is_initialized_ = true;
    return true;
}

bool CSICameraInterface::captureFrame(cv::Mat& frame) {
    if (!is_initialized_ || !cap_.isOpened()) {
        std::cerr << "Camera not initialized or not opened!" << std::endl;
        return false;
    }
    
    bool success = cap_.read(frame);
    if (!success || frame.empty()) {
        std::cerr << "Failed to capture frame from camera!" << std::endl;
        return false;
    }
    
    return true;
}

bool CSICameraInterface::isOpened() const {
    return is_initialized_ && cap_.isOpened();
}

void CSICameraInterface::release() {
    if (cap_.isOpened()) {
        cap_.release();
    }
    is_initialized_ = false;
    frame_width_ = 0;
    frame_height_ = 0;
    framerate_ = 0;
    std::cout << "Camera resources released." << std::endl;
}

} // namespace camera
} // namespace realtime_depth_processor