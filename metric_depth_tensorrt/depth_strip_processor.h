#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <yaml-cpp/yaml.h>

/**
 * @brief Structure to hold polar coordinate data
 */
struct PolarPoint {
    float yaw_degrees;    // Angle in degrees (negative=left, positive=right, 0=center)
    float distance;       // Distance in meters
    
    PolarPoint() : yaw_degrees(0.0f), distance(0.0f) {}
    PolarPoint(float yaw, float dist) : yaw_degrees(yaw), distance(dist) {}
};

/**
 * @brief Configuration structure for depth strip processing
 */
struct DepthStripConfig {
    bool enabled;                    // Enable depth strip processing
    float horizontal_fov_degrees;    // Horizontal field of view in degrees
    int strip_height;               // Height of the strip to extract in pixels
    float gaussian_sigma_factor;    // Factor for Gaussian sigma (sigma = strip_height / factor)
    bool save_polar_data;           // Save polar coordinate data to file
    std::string output_file;        // Output file path for polar data
    
    // Default values
    DepthStripConfig() 
        : enabled(false)
        , horizontal_fov_degrees(73.0f)
        , strip_height(50)
        , gaussian_sigma_factor(6.0f)
        , save_polar_data(false)
        , output_file("polar_coordinates.csv") {}
};

/**
 * @brief Depth strip processor class
 * 
 * This class provides functionality to extract a horizontal strip from a depth image,
 * apply weighted vertical averaging, and convert to polar coordinates.
 * 
 * The processor is designed to work with different camera resolutions and is configurable
 * via YAML configuration files.
 */
class DepthStripProcessor {
public:
    DepthStripProcessor();
    ~DepthStripProcessor();
    
    /**
     * @brief Initialize the processor with configuration
     * @param config Configuration structure
     * @return true if initialization successful
     */
    bool initialize(const DepthStripConfig& config);
    
    /**
     * @brief Load configuration from YAML node
     * @param config_node YAML node containing depth_strip configuration
     * @return true if configuration loaded successfully
     */
    bool loadConfig(const YAML::Node& config_node);
    
    /**
     * @brief Process depth map and extract polar coordinates
     * @param depth_map Input depth map (CV_32F)
     * @return Vector of polar coordinate points
     */
    std::vector<PolarPoint> process(const cv::Mat& depth_map);
    
    /**
     * @brief Check if processor is enabled
     * @return true if enabled
     */
    bool isEnabled() const { return config_.enabled; }
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    const DepthStripConfig& getConfig() const { return config_; }
    
    /**
     * @brief Save polar coordinate data to CSV file
     * @param polar_points Vector of polar points to save
     * @param filename Output CSV filename
     */
    static void savePolarDataToCSV(const std::vector<PolarPoint>& polar_points, 
                                   const std::string& filename);
    
    /**
     * @brief Print polar coordinate data to console
     * @param polar_points Vector of polar points to print
     * @param sample_step Step size for sampling (default: 50)
     */
    static void printPolarData(const std::vector<PolarPoint>& polar_points, 
                               int sample_step = 50);

private:
    DepthStripConfig config_;
    bool initialized_;
    
    /**
     * @brief Extract horizontal strip from center of depth map
     * @param depth_map Input depth map
     * @param strip_height Height of strip to extract
     * @return Extracted strip
     */
    cv::Mat extractHorizontalStrip(const cv::Mat& depth_map, int strip_height);
    
    /**
     * @brief Apply weighted vertical averaging with Gaussian weights
     * @param strip Input strip
     * @return Vector of averaged depth values for each column
     */
    std::vector<float> applyWeightedVerticalAveraging(const cv::Mat& strip);
    
    /**
     * @brief Convert averaged depths to polar coordinates
     * @param averaged_depths Vector of averaged depth values
     * @param image_width Original image width for proper scaling
     * @return Vector of polar coordinate points
     */
    std::vector<PolarPoint> convertToPolarCoordinates(const std::vector<float>& averaged_depths,
                                                      int image_width);
};
