/**
 * Depth calibration tool for measuring and correcting depth offset
 * 
 * This tool helps you determine the correct calibration parameters by:
 * 1. Loading your camera calibration
 * 2. Analyzing depth estimation vs known distances
 * 3. Calculating correction factors
 * 
 * Usage:
 * 1. Place an object at a known distance (e.g., 2m, 5m, 10m)
 * 2. Capture depth image
 * 3. Use this tool to measure the offset
 * 4. Update your config.yaml with the calculated parameters
 */

#include "depth_calibrator.h"
#include <iostream>
#include <fstream>
#include <opencv2/opencv.hpp>

void printUsage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --calibration <file>     Camera calibration XML file" << std::endl;
    std::cout << "  --depth <file>           Depth image file (32-bit float)" << std::endl;
    std::cout << "  --distance <meters>      Known distance to object in meters" << std::endl;
    std::cout << "  --roi <x,y,w,h>         Region of interest (optional)" << std::endl;
    std::cout << "  --help                   Show this help" << std::endl;
    std::cout << std::endl;
    std::cout << "Example:" << std::endl;
    std::cout << "  " << program_name << " --calibration calibration_data/camera_0_calib.xml \\" << std::endl;
    std::cout << "                          --depth depth_2m.bin --distance 2.0" << std::endl;
}

cv::Mat loadDepthImage(const std::string& filename) {
    // Try different formats
    cv::Mat depth;
    
    // Try as OpenCV image first
    depth = cv::imread(filename, cv::IMREAD_ANYDEPTH | cv::IMREAD_ANYCOLOR);
    if (!depth.empty()) {
        if (depth.type() != CV_32F) {
            depth.convertTo(depth, CV_32F);
        }
        return depth;
    }
    
    // Try as binary file (assuming 32-bit float)
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open depth file: " << filename << std::endl;
        return cv::Mat();
    }
    
    // Get file size
    file.seekg(0, std::ios::end);
    size_t file_size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    // Assume square image for simplicity (you may need to adjust this)
    size_t num_pixels = file_size / sizeof(float);
    int size = static_cast<int>(std::sqrt(num_pixels));
    
    if (size * size * sizeof(float) != file_size) {
        std::cerr << "Cannot determine image dimensions from file size" << std::endl;
        return cv::Mat();
    }
    
    depth = cv::Mat(size, size, CV_32F);
    file.read(reinterpret_cast<char*>(depth.data), file_size);
    file.close();
    
    return depth;
}

int main(int argc, char* argv[]) {
    std::string calibration_file;
    std::string depth_file;
    float known_distance = 0.0f;
    cv::Rect roi;
    
    // Parse command line arguments
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else if (arg == "--calibration" && i + 1 < argc) {
            calibration_file = argv[++i];
        } else if (arg == "--depth" && i + 1 < argc) {
            depth_file = argv[++i];
        } else if (arg == "--distance" && i + 1 < argc) {
            known_distance = std::stof(argv[++i]);
        } else if (arg == "--roi" && i + 1 < argc) {
            std::string roi_str = argv[++i];
            // Parse x,y,w,h format
            size_t pos = 0;
            roi.x = std::stoi(roi_str, &pos);
            roi_str = roi_str.substr(pos + 1);
            roi.y = std::stoi(roi_str, &pos);
            roi_str = roi_str.substr(pos + 1);
            roi.width = std::stoi(roi_str, &pos);
            roi_str = roi_str.substr(pos + 1);
            roi.height = std::stoi(roi_str);
        }
    }
    
    if (calibration_file.empty() || depth_file.empty() || known_distance <= 0) {
        std::cerr << "Missing required arguments" << std::endl;
        printUsage(argv[0]);
        return -1;
    }
    
    std::cout << "=== Depth Calibration Tool ===" << std::endl;
    std::cout << "Calibration file: " << calibration_file << std::endl;
    std::cout << "Depth file: " << depth_file << std::endl;
    std::cout << "Known distance: " << known_distance << " meters" << std::endl;
    
    // Initialize depth calibrator
    DepthCalibrator calibrator;
    DepthCalibrationConfig config;
    config.enabled = true;
    config.calibration_file = calibration_file;
    config.use_focal_length_correction = true;
    
    if (!calibrator.initialize(config)) {
        std::cerr << "Failed to initialize depth calibrator" << std::endl;
        return -1;
    }
    
    // Load depth image
    cv::Mat depth_map = loadDepthImage(depth_file);
    if (depth_map.empty()) {
        std::cerr << "Failed to load depth image" << std::endl;
        return -1;
    }
    
    std::cout << "Loaded depth image: " << depth_map.cols << "x" << depth_map.rows << std::endl;
    
    // Estimate scale factor
    float scale_factor = calibrator.estimateScaleFromKnownDistance(depth_map, known_distance, roi);
    
    // Calculate offset (if any)
    cv::Scalar mean_depth, std_depth;
    cv::Mat mask = depth_map > 0; // Valid depth pixels
    if (roi.width > 0 && roi.height > 0) {
        cv::meanStdDev(depth_map(roi), mean_depth, std_depth, mask(roi));
    } else {
        cv::meanStdDev(depth_map, mean_depth, std_depth, mask);
    }
    
    float measured_mean = mean_depth[0];
    float offset = known_distance - (measured_mean * scale_factor);
    
    std::cout << "\n=== Calibration Results ===" << std::endl;
    std::cout << "Measured mean depth: " << measured_mean << " meters" << std::endl;
    std::cout << "Known distance: " << known_distance << " meters" << std::endl;
    std::cout << "Recommended scale factor: " << scale_factor << std::endl;
    std::cout << "Recommended offset: " << offset << " meters" << std::endl;
    
    std::cout << "\n=== Configuration for config.yaml ===" << std::endl;
    std::cout << "depth_calibration:" << std::endl;
    std::cout << "  enabled: true" << std::endl;
    std::cout << "  use_manual_correction: true" << std::endl;
    std::cout << "  depth_scale_factor: " << scale_factor << std::endl;
    std::cout << "  depth_offset_meters: " << offset << std::endl;
    
    // Show automatic focal length correction info
    const auto& camera_intrinsics = calibrator.getCameraIntrinsics();
    if (camera_intrinsics.fx > 0) {
        double focal_ratio = camera_intrinsics.fx / config.model_fx;
        std::cout << "\n=== Automatic Focal Length Correction ===" << std::endl;
        std::cout << "Your camera focal length: " << camera_intrinsics.fx << " pixels" << std::endl;
        std::cout << "Model training focal length: " << config.model_fx << " pixels" << std::endl;
        std::cout << "Focal length ratio: " << focal_ratio << std::endl;
        std::cout << "This explains the ~" << focal_ratio << "x depth scaling issue." << std::endl;
    }
    
    std::cout << "\n=== Next Steps ===" << std::endl;
    std::cout << "1. Update your config.yaml with the recommended values above" << std::endl;
    std::cout << "2. Test with objects at different distances" << std::endl;
    std::cout << "3. Fine-tune the parameters if needed" << std::endl;
    
    return 0;
}
