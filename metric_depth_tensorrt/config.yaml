# Depth Anything V2 TensorRT Inference Configuration

# Model Configuration
model:
  engine_path: "/home/<USER>/code/metric_depth/metric_depth_tensorrt/models/depth_anything_v2_metric_vkitti_vits.engine"
  input_size: 518  # Input image size (must be multiple of 14)
  max_depth: 80.0  # Maximum depth value in meters
  encoder: "vits"  # Encoder type: vits, vitb, vitl, vitg

# Camera Configuration
camera:
  sensor_id: 0      # CSI camera sensor ID (0 for single camera)
  width: 1920       # Camera capture width
  height: 1080      # Camera capture height
  fps: 30           # Target FPS
  sensor_mode: 2    # Camera sensor mode (2 for 1920x1080)
  flip_method: 0    # Image flip method (0=none, 1=ccw90, 2=180, 3=cw90, 4=horizontal, 5=upper-right-diagonal, 6=vertical, 7=upper-left-diagonal)
  
# Preprocessing Configuration
preprocessing:
  # ImageNet normalization values
  mean: [0.485, 0.456, 0.406]  # RGB mean values
  std: [0.229, 0.224, 0.225]   # RGB standard deviation values
  keep_aspect_ratio: true
  resize_method: "lower_bound"  # lower_bound, upper_bound, minimal
  ensure_multiple_of: 14
  interpolation: "cubic"  # cubic, linear, nearest

# Postprocessing Configuration
postprocessing:
  output_original_size: true  # Resize depth map to original camera resolution
  interpolation: "bilinear"   # bilinear, nearest

# Visualization Configuration
visualization:
  debug: true              # Enable debug visualization
  window_width: 1080       # Debug window width
  window_height: 720       # Debug window height
  colormap: "spectral"     # Colormap for depth visualization: spectral, jet, viridis, plasma
  show_fps: true           # Show FPS counter
  show_depth_range: true   # Show min/max depth values
  
# Performance Configuration
performance:
  use_cuda_stream: true    # Use CUDA streams for async processing
  max_batch_size: 1        # Maximum batch size for inference
  workspace_size: 1073741824  # TensorRT workspace size in bytes (1GB)
  
# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  log_inference_time: true
  log_preprocessing_time: false
  log_postprocessing_time: false

# Output Configuration
output:
  save_depth_maps: true   # Save depth maps to disk
  output_directory: "output"
  depth_format: "npy"      # npy, png, exr
  save_rgb: true          # Save RGB frames
  
# TensorRT Configuration
tensorrt:
  precision: "FP16"        # FP32, FP16, INT8
  dla_core: -1             # DLA core to use (-1 for GPU, 0/1 for DLA on Jetson)
  allow_gpu_fallback: true # Allow GPU fallback for DLA

# Depth Calibration Configuration
depth_calibration:
  enabled: false                     # Enable depth calibration
  calibration_file: "/home/<USER>/code/metric_depth/calibration_data/camera_0_calibration_20250828_140956.xml"  # Path to camera calibration XML
  focal_length_scale_factor: 1    # Manual focal length scale factor
  depth_offset_meters: -0.532881          # Manual depth offset in meters
  depth_scale_factor: 0.347401           # Manual depth scale factor
  use_focal_length_correction: false # Use automatic focal length correction
  use_manual_correction: true      # Use manual offset and scale
  # Model training parameters (Depth-Anything-V2 Virtual KITTI)
  model_fx: 725.0087               # Model training focal length X
  model_fy: 725.0087               # Model training focal length Y
  model_width: 1241                # Model training image width
  model_height: 374                # Model training image height

# Depth Strip Processing Configuration
depth_strip:
  enabled: true                   # Enable depth strip processing
  horizontal_fov_degrees: 73.0      # Horizontal field of view in degrees
  strip_height: 50                  # Height of the strip to extract in pixels
  gaussian_sigma_factor: 6.0        # Factor for Gaussian sigma (sigma = strip_height / factor)
  save_polar_data: false            # Save polar coordinate data to CSV file
  output_file: "polar_coordinates.csv"  # Output file path for polar data

# Radar Visualizer Configuration
radar_visualizer:
  enabled: true                     # Enable radar visualization
  max_radius_meters: 10.0           # Maximum radar radius in meters
  window_width: 800                 # Radar window width in pixels
  window_height: 800                # Radar window height in pixels
  show_grid: true                   # Show radar grid lines
  show_range_rings: true            # Show range rings
  show_angle_lines: true            # Show angle lines
  grid_divisions: 8                 # Number of grid divisions (range rings)
  point_size: 3.0                   # Size of object points in pixels
  background_color: [0, 0, 0]       # Background color [B, G, R]
  grid_color: [50, 50, 50]          # Grid color [B, G, R]
  range_ring_color: [100, 100, 100] # Range ring color [B, G, R]
  angle_line_color: [80, 80, 80]    # Angle line color [B, G, R]
  object_color: [0, 255, 0]         # Object point color [B, G, R] (Green)
  center_color: [0, 0, 255]         # Center point color [B, G, R] (Red)
  save_radar_images: false          # Save radar images to disk
  output_directory: "radar_output"  # Output directory for saved images
  show_distance_labels: true        # Show distance labels on range rings
  show_angle_labels: true           # Show angle labels
  update_rate_hz: 10.0              # Update rate in Hz (0 = as fast as possible)

# MAVLink Communication Configuration
mavlink:
  enabled: true                     # Enable MAVLink communication
  serial_port: "/dev/ttyACM0"       # Serial port for TELEM2 connection
  baudrate: 921600                  # Baudrate (115200 for TELEM2)
  system_id: 1                      # Our system ID
  component_id: 158                 # Our component ID (MAV_COMP_ID_PERIPHERAL)
  target_system_id: 1               # Target system ID (autopilot)
  target_component_id: 1            # Target component ID (MAV_COMP_ID_AUTOPILOT1)
  heartbeat_rate_hz: 15.0           # Heartbeat transmission rate (Hz)
  obstacle_distance_rate_hz: 30.0   # OBSTACLE_DISTANCE message rate (Hz)
  debug_messages: false              # Enable debug message printing
  # OBSTACLE_DISTANCE configuration
  min_distance: 0.2                 # Minimum distance in meters
  max_distance: 50.0                # Maximum distance in meters
  increment_deg: 5.0                # Angular increment in degrees (5° = 72 sectors)

  # Recording control via RC channel
  recording_channel: 9             # RC channel number for recording control (1-18)
  recording_pwm_threshold: 1500     # PWM threshold for recording (>= start, < stop)
  recording_pwm_high: 1800          # PWM value considered "high" (recording ON)
  recording_pwm_low: 1200           # PWM value considered "low" (recording OFF)
  
# Platform-specific paths (auto-detected)
paths:
  # NVIDIA RTX A5000 (Desktop)
  desktop:
    tensorrt_root: "/usr/local/TensorRT"
    cuda_root: "/usr/local/cuda"
    opencv_root: "/usr/local"
    
  # NVIDIA Jetson ORIN NX
  jetson:
    tensorrt_root: "/usr/lib/aarch64-linux-gnu"
    cuda_root: "/usr/local/cuda"
    opencv_root: "/usr"
