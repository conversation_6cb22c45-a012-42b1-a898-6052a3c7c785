#pragma once

#include <opencv2/opencv.hpp>
#include <string>

namespace realtime_depth_processor {
namespace camera {

/**
 * @brief Camera interface for CSI IMX219 camera using GStreamer
 * Supports 1920x1080 @ 30fps (sensor mode 2)
 */
class CSICameraInterface {
public:
    CSICameraInterface();
    ~CSICameraInterface();
    
    /**
     * @brief Initialize camera with specified parameters
     * @param sensor_id Camera sensor ID (0 for single camera)
     * @param width Frame width (1920)
     * @param height Frame height (1080) 
     * @param framerate Target framerate (30)
     * @param sensor_mode Camera sensor mode (2 for 1920x1080)
     * @param flip_method Image flip method (0=none, 1=ccw90, 2=180, 3=cw90, 4=horizontal, 5=upper-right-diagonal, 6=vertical, 7=upper-left-diagonal)
     * @return true if initialization successful
     */
    bool initialize(int sensor_id = 0, int width = 1920, int height = 1080, 
                   int framerate = 30, int sensor_mode = 2, int flip_method = 2);
    
    /**
     * @brief Capture a frame from the camera
     * @param frame Output frame
     * @return true if frame captured successfully
     */
    bool captureFrame(cv::Mat& frame);
    
    /**
     * @brief Check if camera is opened and ready
     * @return true if camera is ready
     */
    bool isOpened() const;
    
    /**
     * @brief Release camera resources
     */
    void release();
    
    /**
     * @brief Get current frame width
     */
    int getFrameWidth() const { return frame_width_; }
    
    /**
     * @brief Get current frame height  
     */
    int getFrameHeight() const { return frame_height_; }
    
    /**
     * @brief Get current framerate
     */
    int getFramerate() const { return framerate_; }

private:
    /**
     * @brief Generate optimized GStreamer pipeline string
     * @param sensor_id Camera sensor ID
     * @param width Frame width
     * @param height Frame height
     * @param framerate Target framerate
     * @param sensor_mode Camera sensor mode
     * @param flip_method Image flip method
     * @return GStreamer pipeline string
     */
    std::string gstreamer_pipeline_optimized(int sensor_id, int width, int height, 
                                            int framerate, int sensor_mode, int flip_method);
    
    cv::VideoCapture cap_;
    int frame_width_;
    int frame_height_;
    int framerate_;
    bool is_initialized_;
};

} // namespace camera
} // namespace realtime_depth_processor