# Depth Anything V2 TensorRT Inference with CSI Camera

This project provides real-time depth estimation using Depth Anything V2 model with TensorRT optimization and CSI camera support for NVIDIA Jetson devices.

## Features

- **Real-time CSI camera capture** using GStreamer pipeline
- **TensorRT optimized inference** for fast depth estimation
- **Live visualization** with side-by-side RGB and depth display
- **YAML configuration** for easy parameter adjustment
- **Cross-platform support** for Jetson ORIN NX and desktop development
- **Depth output in meters** as requested

## Prerequisites

### On Jetson Device:
- NVIDIA Jetson ORIN NX with JetPack
- TensorRT (usually pre-installed with JetPack)
- OpenCV with GStreamer support
- yaml-cpp library
- CSI camera (e.g., IMX219)

### Installation of Dependencies:
```bash
sudo apt update
sudo apt install libyaml-cpp-dev libopencv-dev
```

## Build Instructions

1. **Clone and navigate to the project:**
```bash
cd metric_depth_tensorrt
```

2. **Make build script executable:**
```bash
chmod +x build.sh
```

3. **Build the project:**
```bash
./build.sh
```

## Usage

### 1. Convert PyTorch Model to ONNX (on development machine):
```bash
python convert_to_onnx.py --checkpoint checkpoints/depth_anything_v2_metric_hypersim_vitl.pth --output models/depth_anything_v2.onnx --encoder vitl
```

### 2. Convert ONNX to TensorRT Engine (on Jetson):
```bash
trtexec --onnx=depth_anything_v2.onnx \
        --saveEngine=depth_anything_v2.engine \
        --fp16 \
        --workspace=512 \
        --useDLACore=0 \
        --allowGPUFallback \
        --minShapes=input:1x3x518x518 \
        --optShapes=input:1x3x518x518 \
        --maxShapes=input:1x3x518x518 \
        --verbose
```

### 3. Update Configuration:
Edit `config.yaml` to set the correct paths:
```yaml
model:
  engine_path: "models/depth_anything_v2.engine"
```

### 4. Run the Application:
```bash
cd build
./depth-anything-tensorrt ../config.yaml
```

## Configuration

The `config.yaml` file contains all configurable parameters:

### Camera Settings:
- `sensor_id`: CSI camera sensor ID (0 for single camera)
- `width/height`: Camera resolution (1920x1080 recommended)
- `fps`: Target framerate (30 fps)
- `sensor_mode`: Camera sensor mode (2 for 1920x1080)
- `flip_method`: Image rotation (2 for 180° flip)

### Model Settings:
- `engine_path`: Path to TensorRT engine file
- `input_size`: Model input size (518)
- `max_depth`: Maximum depth value in meters (20.0)

### Visualization:
- `debug`: Enable/disable live visualization
- `window_width/height`: Display window size (1080x720)
- `colormap`: Depth colormap (spectral, jet, viridis, plasma)

## Controls

When running with debug visualization:
- **'q' or ESC**: Quit application
- **'s'**: Save current RGB and depth frames
- **FPS counter**: Shows real-time performance

## File Structure

```
metric_depth_tensorrt/
├── main.cpp              # Main application entry point
├── depth_inference.h     # Main inference class header
├── depth_inference.cpp   # Main inference implementation
├── camera.h              # CSI camera interface header
├── camera.cpp            # CSI camera implementation
├── config.yaml           # Configuration file
├── CMakeLists.txt        # Build configuration
├── build.sh              # Build script
└── README.md             # This file
```

## Troubleshooting

### Camera Issues:
1. Check camera connection: `ls /dev/video*`
2. Test GStreamer pipeline:
```bash
gst-launch-1.0 nvarguscamerasrc sensor-id=0 ! 'video/x-raw(memory:NVMM), width=1920, height=1080, format=NV12, framerate=30/1' ! nvvidconv ! 'video/x-raw, format=BGRx' ! videoconvert ! 'video/x-raw, format=BGR' ! filesink location=test.raw
```

### TensorRT Issues:
1. Verify TensorRT installation: `dpkg -l | grep tensorrt`
2. Check engine file compatibility with current TensorRT version
3. Regenerate engine file if needed

### Build Issues:
1. Ensure all dependencies are installed
2. Check CMake finds all required libraries
3. Verify TensorRT headers are accessible

## Performance Notes

- **Expected FPS**: 15-30 FPS on Jetson ORIN NX depending on model size
- **Memory Usage**: ~2-4GB GPU memory depending on model
- **Optimization**: Use FP16 precision for better performance
- **DLA Usage**: Can offload some operations to DLA cores for power efficiency

## Output

- **Depth values**: Output in meters as requested
- **Visualization**: Real-time side-by-side RGB and depth display
- **Saved files**: RGB frames (.jpg) and depth maps (.png/.bin)


Depth calibration process
1. Disable depth calibrator in config.yaml depth_calibration: enabled: false 
2. Start inference and in output config part make save_depth_maps: true save_rgb: true 
3. Open one good image in https://wsmlby.github.io/webtools/bbox.html and record x, y or known depth object 
4. ./calibrate_depth --calibration /home/<USER>/code/metric_depth/calibration_data/camera_0_calibration_20250828_140956.xml --depth '/home/<USER>/code/metric_depth/metric_depth_tensorrt/build/output/depth_20250902_154253_097.bin'  --distance 1.47 --roi 711,444,200,100
