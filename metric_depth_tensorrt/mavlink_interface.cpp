#include "mavlink_interface.h"
#include <iostream>
#include <fcntl.h>
#include <termios.h>
#include <unistd.h>
#include <cstring>
#include <algorithm>
#include <errno.h>

MAVLinkInterface::MAVLinkInterface()
    : initialized_(false), running_(false), serial_fd_(-1),
      autopilot_detected_(false), sent_heartbeats_(0), sent_obstacles_(0),
      rc_data_received_(false) {
}

MAVLinkInterface::~MAVLinkInterface() {
    stop();
}

bool MAVLinkInterface::initialize(const MAVLinkConfig& config) {
    config_ = config;
    
    if (!config_.enabled) {
        initialized_ = true;
        return true;
    }
    
    initialized_ = true;
    
    std::cout << "MAVLinkInterface initialized:" << std::endl;
    std::cout << "  Enabled: " << (config_.enabled ? "true" : "false") << std::endl;
    std::cout << "  Serial port: " << config_.serial_port << std::endl;
    std::cout << "  Baudrate: " << config_.baudrate << std::endl;
    std::cout << "  System ID: " << static_cast<int>(config_.system_id) << std::endl;
    std::cout << "  Component ID: " << static_cast<int>(config_.component_id) << std::endl;
    std::cout << "  Heartbeat rate: " << config_.heartbeat_rate_hz << " Hz" << std::endl;
    
    return true;
}

bool MAVLinkInterface::loadConfig(const YAML::Node& config_node) {
    try {
        if (!config_node) {
            std::cout << "No mavlink configuration found, using defaults (disabled)" << std::endl;
            config_.enabled = false;
            return true;
        }
        
        config_.enabled = config_node["enabled"].as<bool>(false);
        config_.serial_port = config_node["serial_port"].as<std::string>("/dev/ttyTHS0");
        config_.baudrate = config_node["baudrate"].as<int>(115200);
        config_.system_id = static_cast<uint8_t>(config_node["system_id"].as<int>(1));
        config_.component_id = static_cast<uint8_t>(config_node["component_id"].as<int>(158));
        config_.target_system_id = static_cast<uint8_t>(config_node["target_system_id"].as<int>(1));
        config_.target_component_id = static_cast<uint8_t>(config_node["target_component_id"].as<int>(1));
        config_.heartbeat_rate_hz = config_node["heartbeat_rate_hz"].as<float>(15.0f);
        config_.obstacle_distance_rate_hz = config_node["obstacle_distance_rate_hz"].as<float>(5.0f);
        config_.debug_messages = config_node["debug_messages"].as<bool>(false);
        config_.min_distance = config_node["min_distance"].as<float>(0.2f);
        config_.max_distance = config_node["max_distance"].as<float>(80.0f);
        config_.increment_deg = config_node["increment_deg"].as<float>(5.0f);
        config_.recording_channel = static_cast<uint8_t>(config_node["recording_channel"].as<int>(16));
        config_.recording_pwm_threshold = static_cast<uint16_t>(config_node["recording_pwm_threshold"].as<int>(1500));
        config_.recording_pwm_high = static_cast<uint16_t>(config_node["recording_pwm_high"].as<int>(1800));
        config_.recording_pwm_low = static_cast<uint16_t>(config_node["recording_pwm_low"].as<int>(1200));
        
        return initialize(config_);
    } catch (const std::exception& e) {
        std::cerr << "Error loading MAVLink configuration: " << e.what() << std::endl;
        config_.enabled = false;
        return false;
    }
}

bool MAVLinkInterface::start() {
    if (!initialized_ || !config_.enabled || running_) {
        return false;
    }
    
    // Setup serial port
    if (!setupSerialPort()) {
        std::cerr << "Failed to setup serial port" << std::endl;
        return false;
    }
    
    running_ = true;
    last_heartbeat_sent_time_ = std::chrono::high_resolution_clock::now();
    last_obstacle_distance_time_ = std::chrono::high_resolution_clock::now();
    last_rc_request_time_ = std::chrono::high_resolution_clock::now();
    
    // Start threads
    read_thread_ = std::thread(&MAVLinkInterface::readThreadFunction, this);
    heartbeat_thread_ = std::thread(&MAVLinkInterface::heartbeatThreadFunction, this);
    
    std::cout << "MAVLink communication started on " << config_.serial_port << std::endl;
    return true;
}

void MAVLinkInterface::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // Join threads
    if (read_thread_.joinable()) {
        read_thread_.join();
    }
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    
    // Close serial port
    if (serial_fd_ != -1) {
        close(serial_fd_);
        serial_fd_ = -1;
    }
    
    std::cout << "MAVLink communication stopped" << std::endl;
}

bool MAVLinkInterface::isConnected() const {
    return autopilot_detected_.load() && checkConnectionStatus();
}

bool MAVLinkInterface::sendObstacleDistanceFromPolar(const std::vector<PolarPoint>& polar_points) {
    if (polar_points.empty() || !isConnected()) {
        return false;
    }
    
    // Check rate limiting
    if (!shouldSendObstacleDistance()) {
        return true; // Not an error, just rate limited
    }
    
    float distances[72];
    convertPolarToObstacleArray(polar_points, distances);
    
    bool result = sendObstacleDistance(distances);
    if (result) {
        last_obstacle_distance_time_ = std::chrono::high_resolution_clock::now();
    }
    
    return result;
}

bool MAVLinkInterface::sendObstacleDistance(const float* distances, float angle_offset) {
    if (!running_ || !isConnected()) {
        return false;
    }
    
    mavlink_message_t msg;
    
    // Convert distances to centimeters
    uint16_t distances_cm[72];
    for (int i = 0; i < 72; ++i) {
        if (distances[i] > 0 && distances[i] <= config_.max_distance) {
            distances_cm[i] = static_cast<uint16_t>(distances[i] * 100); // cm
        } else {
            distances_cm[i] = static_cast<uint16_t>(config_.max_distance * 100 + 1); // Invalid
        }
    }
    
    // Use the same format as your working Arduino code
    mavlink_msg_obstacle_distance_pack(
        config_.system_id,
        config_.component_id,
        &msg,
        getCurrentTimeUsec(),                           // time_usec
        MAV_DISTANCE_SENSOR_LASER,                     // sensor_type
        distances_cm,                                  // distances array
        0,                                             // increment (not used)
        static_cast<uint16_t>(config_.min_distance * 100), // min_distance (cm)
        static_cast<uint16_t>(config_.max_distance * 100), // max_distance (cm)
        config_.increment_deg,                         // increment_f
        angle_offset,                                  // angle_offset
        MAV_FRAME_BODY_FRD                            // frame
    );
    
    sendMessage(msg);
    sent_obstacles_++;
    
    if (config_.debug_messages) {
        // Count valid bins
        int valid_bins = 0;
        for (int i = 0; i < 72; ++i) {
            if (distances_cm[i] <= config_.max_distance * 100) {
                valid_bins++;
            }
        }
        std::cout << "OBSTACLE_DISTANCE #" << sent_obstacles_.load() 
                  << " sent. Valid bins: " << valid_bins << "/72" << std::endl;
    }
    
    return true;
}

AutopilotData MAVLinkInterface::getAutopilotData() const {
    std::lock_guard<std::mutex> lock(status_mutex_);
    return autopilot_data_;
}

bool MAVLinkInterface::isRecordingActive() const {
    std::lock_guard<std::mutex> lock(status_mutex_);
    return autopilot_data_.recording_active;
}

void MAVLinkInterface::setMessageCallback(std::function<void(const mavlink_message_t&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    message_callback_ = callback;
}

bool MAVLinkInterface::setupSerialPort() {
    serial_fd_ = open(config_.serial_port.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
    if (serial_fd_ == -1) {
        std::cerr << "Error opening serial port: " << config_.serial_port << std::endl;
        return false;
    }

    struct termios options;
    tcgetattr(serial_fd_, &options);

    // Set baud rate
    speed_t baud;
    switch(config_.baudrate) {
        case 57600: baud = B57600; break;
        case 115200: baud = B115200; break;
        case 921600: baud = B921600; break;
        default: baud = B115200; break;
    }

    cfsetispeed(&options, baud);
    cfsetospeed(&options, baud);

    // 8N1 configuration
    options.c_cflag &= ~PARENB;   // No parity
    options.c_cflag &= ~CSTOPB;   // 1 stop bit
    options.c_cflag &= ~CSIZE;    // Clear size bits
    options.c_cflag |= CS8;       // 8 data bits
    options.c_cflag |= CREAD | CLOCAL; // Enable receiver, ignore modem lines

    // Raw input/output
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    options.c_iflag &= ~(IXON | IXOFF | IXANY);
    options.c_oflag &= ~OPOST;

    tcsetattr(serial_fd_, TCSANOW, &options);

    // Set non-blocking mode for reading
    fcntl(serial_fd_, F_SETFL, FNDELAY);

    std::cout << "Serial port " << config_.serial_port << " opened at " << config_.baudrate << " baud" << std::endl;
    return true;
}

void MAVLinkInterface::readThreadFunction() {
    uint8_t buffer[MAVLINK_MAX_PACKET_LEN];
    mavlink_message_t msg;
    mavlink_status_t status;

    std::cout << "MAVLink read thread started" << std::endl;

    while (running_) {
        int bytes_read = read(serial_fd_, buffer, sizeof(buffer));
        if (bytes_read > 0) {
            for (int i = 0; i < bytes_read; i++) {
                if (mavlink_parse_char(MAVLINK_COMM_0, buffer[i], &msg, &status)) {
                    handleMessage(msg);

                    // Call user callback if set
                    {
                        std::lock_guard<std::mutex> lock(callback_mutex_);
                        if (message_callback_) {
                            message_callback_(msg);
                        }
                    }
                }
            }
        } else if (bytes_read < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
            if (running_) {
                std::cerr << "Serial read error: " << strerror(errno) << std::endl;
            }
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    std::cout << "MAVLink read thread stopped" << std::endl;
}

void MAVLinkInterface::heartbeatThreadFunction() {
    std::cout << "MAVLink heartbeat thread started - sending at " << config_.heartbeat_rate_hz << " Hz" << std::endl;

    while (running_) {
        // Send heartbeat
        sendHeartbeat();

        // Request RC data streams if needed
        if (autopilot_detected_.load() && shouldRequestRCData()) {
            requestRCDataStreams();
            last_rc_request_time_ = std::chrono::high_resolution_clock::now();
        }

        // Sleep for heartbeat interval
        int interval_ms = static_cast<int>(1000.0f / config_.heartbeat_rate_hz);
        std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms));
    }

    std::cout << "MAVLink heartbeat thread stopped" << std::endl;
}

void MAVLinkInterface::sendMessage(const mavlink_message_t& msg) {
    if (serial_fd_ == -1) {
        return;
    }

    uint8_t buffer[MAVLINK_MAX_PACKET_LEN];
    uint16_t len = mavlink_msg_to_send_buffer(buffer, &msg);

    if (write(serial_fd_, buffer, len) != len) {
        if (config_.debug_messages) {
            std::cerr << "Error writing to serial port" << std::endl;
        }
    }
}

void MAVLinkInterface::handleMessage(const mavlink_message_t& msg) {
    switch (msg.msgid) {
        case MAVLINK_MSG_ID_HEARTBEAT: {
            mavlink_heartbeat_t heartbeat;
            mavlink_msg_heartbeat_decode(&msg, &heartbeat);

            // Check if this is from an autopilot
            if (heartbeat.autopilot != MAV_AUTOPILOT_INVALID &&
                msg.compid == MAV_COMP_ID_AUTOPILOT1) {

                std::lock_guard<std::mutex> lock(status_mutex_);
                autopilot_data_.received_heartbeats++;
                autopilot_data_.last_heartbeat_time = std::chrono::high_resolution_clock::now();
                autopilot_data_.connected = true;
                autopilot_data_.autopilot_type = heartbeat.autopilot;
                autopilot_data_.system_status = heartbeat.system_status;
                autopilot_data_.armed = (heartbeat.base_mode & MAV_MODE_FLAG_SAFETY_ARMED) != 0;

                if (!autopilot_detected_.load()) {
                    autopilot_detected_ = true;
                    std::cout << "Autopilot detected! System ID: " << static_cast<int>(msg.sysid)
                              << ", Component ID: " << static_cast<int>(msg.compid)
                              << ", Type: " << static_cast<int>(heartbeat.type)
                              << ", Autopilot: " << static_cast<int>(heartbeat.autopilot) << std::endl;
                }

                if (config_.debug_messages) {
                    std::cout << "Received heartbeat from system " << static_cast<int>(msg.sysid)
                              << " component " << static_cast<int>(msg.compid) << std::endl;
                }
            }
            break;
        }

        case MAVLINK_MSG_ID_RC_CHANNELS: {
            mavlink_rc_channels_t rc_channels;
            mavlink_msg_rc_channels_decode(&msg, &rc_channels);

            std::lock_guard<std::mutex> lock(status_mutex_);

            // Update RC channel data (convert from 1-based to 0-based indexing)
            autopilot_data_.rc_channels[0] = rc_channels.chan1_raw;
            autopilot_data_.rc_channels[1] = rc_channels.chan2_raw;
            autopilot_data_.rc_channels[2] = rc_channels.chan3_raw;
            autopilot_data_.rc_channels[3] = rc_channels.chan4_raw;
            autopilot_data_.rc_channels[4] = rc_channels.chan5_raw;
            autopilot_data_.rc_channels[5] = rc_channels.chan6_raw;
            autopilot_data_.rc_channels[6] = rc_channels.chan7_raw;
            autopilot_data_.rc_channels[7] = rc_channels.chan8_raw;
            autopilot_data_.rc_channels[8] = rc_channels.chan9_raw;
            autopilot_data_.rc_channels[9] = rc_channels.chan10_raw;
            autopilot_data_.rc_channels[10] = rc_channels.chan11_raw;
            autopilot_data_.rc_channels[11] = rc_channels.chan12_raw;
            autopilot_data_.rc_channels[12] = rc_channels.chan13_raw;
            autopilot_data_.rc_channels[13] = rc_channels.chan14_raw;
            autopilot_data_.rc_channels[14] = rc_channels.chan15_raw;
            autopilot_data_.rc_channels[15] = rc_channels.chan16_raw;
            autopilot_data_.rc_channels[16] = rc_channels.chan17_raw;
            autopilot_data_.rc_channels[17] = rc_channels.chan18_raw;

            autopilot_data_.rc_channels_valid = true;

            // Mark RC data as received (stop requesting)
            if (!rc_data_received_.load()) {
                rc_data_received_ = true;
                std::cout << "RC channel data stream established!" << std::endl;
            }

            // Check recording channel (convert from 1-based to 0-based)
            if (config_.recording_channel >= 1 && config_.recording_channel <= 18) {
                uint16_t channel_value = autopilot_data_.rc_channels[config_.recording_channel - 1];
                bool was_recording = autopilot_data_.recording_active;
                autopilot_data_.recording_active = (channel_value >= config_.recording_pwm_threshold);

                // Log recording state changes
                if (was_recording != autopilot_data_.recording_active) {
                    std::cout << "Recording " << (autopilot_data_.recording_active ? "STARTED" : "STOPPED")
                              << " - Channel " << static_cast<int>(config_.recording_channel)
                              << " PWM: " << channel_value << std::endl;
                }

                if (config_.debug_messages) {
                    std::cout << "RC Channel " << static_cast<int>(config_.recording_channel)
                              << " PWM: " << channel_value
                              << " (Recording: " << (autopilot_data_.recording_active ? "ON" : "OFF") << ")" << std::endl;
                }
            }

            break;
        }

        case MAVLINK_MSG_ID_RC_CHANNELS_RAW: {
            mavlink_rc_channels_raw_t rc_raw;
            mavlink_msg_rc_channels_raw_decode(&msg, &rc_raw);

            std::lock_guard<std::mutex> lock(status_mutex_);

            // Update RC channel data from RAW message (only first 8 channels)
            autopilot_data_.rc_channels[0] = rc_raw.chan1_raw;
            autopilot_data_.rc_channels[1] = rc_raw.chan2_raw;
            autopilot_data_.rc_channels[2] = rc_raw.chan3_raw;
            autopilot_data_.rc_channels[3] = rc_raw.chan4_raw;
            autopilot_data_.rc_channels[4] = rc_raw.chan5_raw;
            autopilot_data_.rc_channels[5] = rc_raw.chan6_raw;
            autopilot_data_.rc_channels[6] = rc_raw.chan7_raw;
            autopilot_data_.rc_channels[7] = rc_raw.chan8_raw;

            autopilot_data_.rc_channels_valid = true;

            // Mark RC data as received (stop requesting)
            if (!rc_data_received_.load()) {
                rc_data_received_ = true;
                std::cout << "RC channel data stream established (RAW)!" << std::endl;
            }

            // Check recording channel (convert from 1-based to 0-based)
            if (config_.recording_channel >= 1 && config_.recording_channel <= 8) {
                uint16_t channel_value = autopilot_data_.rc_channels[config_.recording_channel - 1];
                bool was_recording = autopilot_data_.recording_active;
                autopilot_data_.recording_active = (channel_value >= config_.recording_pwm_threshold);

                // Log recording state changes
                if (was_recording != autopilot_data_.recording_active) {
                    std::cout << "Recording " << (autopilot_data_.recording_active ? "STARTED" : "STOPPED")
                              << " - Channel " << static_cast<int>(config_.recording_channel)
                              << " PWM: " << channel_value << std::endl;
                }

                if (config_.debug_messages) {
                    std::cout << "RC Channel " << static_cast<int>(config_.recording_channel)
                              << " PWM: " << channel_value
                              << " (Recording: " << (autopilot_data_.recording_active ? "ON" : "OFF") << ")" << std::endl;
                }
            }

            break;
        }

        default:
            // Optionally log other message types
            if (config_.debug_messages) {
                std::cout << "Received message ID: " << msg.msgid << std::endl;
            }
            break;
    }
}

void MAVLinkInterface::sendHeartbeat() {
    mavlink_message_t msg;

    // Use the same format as your working companion code
    mavlink_msg_heartbeat_pack(
        config_.system_id,
        config_.component_id,
        &msg,
        MAV_TYPE_ONBOARD_CONTROLLER,    // type
        MAV_AUTOPILOT_INVALID,          // autopilot
        0,                              // base_mode
        0,                              // custom_mode
        MAV_STATE_ACTIVE                // system_status
    );

    sendMessage(msg);
    sent_heartbeats_++;

    if (config_.debug_messages) {
        std::string status = autopilot_detected_.load() ? "🔗" : "📡";
        std::cout << status << " Heartbeat #" << sent_heartbeats_.load() << " sent - System ID: "
                  << static_cast<int>(config_.system_id) << ", Component ID: " << static_cast<int>(config_.component_id);
        if (autopilot_detected_.load()) {
            std::lock_guard<std::mutex> lock(status_mutex_);
            std::cout << " (Autopilot detected, RX: " << autopilot_data_.received_heartbeats << ")";
        }
        std::cout << std::endl;
    }
}

void MAVLinkInterface::convertPolarToObstacleArray(const std::vector<PolarPoint>& polar_points,
                                                  float* distances, float angle_offset) {
    // Initialize all distances to invalid
    for (int i = 0; i < 72; ++i) {
        distances[i] = -1.0f; // Invalid distance
    }

    // Convert polar points to obstacle distance array
    // OBSTACLE_DISTANCE uses 5-degree increments (72 sectors for 360 degrees)
    for (const auto& point : polar_points) {
        // Adjust angle for offset and convert to obstacle distance coordinate system
        float adjusted_angle = point.yaw_degrees + angle_offset;

        // Normalize angle to 0-360 range
        while (adjusted_angle < 0) adjusted_angle += 360.0f;
        while (adjusted_angle >= 360.0f) adjusted_angle -= 360.0f;

        // Convert to array index (5-degree increments)
        int index = static_cast<int>(adjusted_angle / 5.0f);
        if (index >= 0 && index < 72) {
            // Use minimum distance if multiple points map to same sector
            if (distances[index] < 0 || point.distance < distances[index]) {
                distances[index] = point.distance;
            }
        }
    }
}

bool MAVLinkInterface::shouldSendObstacleDistance() {
    if (config_.obstacle_distance_rate_hz <= 0) {
        return false;
    }

    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration<double>(now - last_obstacle_distance_time_).count();
    double interval = 1.0 / config_.obstacle_distance_rate_hz;

    return elapsed >= interval;
}

uint64_t MAVLinkInterface::getCurrentTimeUsec() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

bool MAVLinkInterface::checkConnectionStatus() const {
    if (!autopilot_detected_.load()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(status_mutex_);
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
        now - autopilot_data_.last_heartbeat_time).count();

    // Consider disconnected if no heartbeat received for 5 seconds
    return elapsed < 5;
}

void MAVLinkInterface::requestRCDataStreams() {
    if (!running_ || serial_fd_ == -1) {
        return;
    }

    std::cout << "Requesting RC channel data streams..." << std::endl;

    // Method 1: MAV_CMD_SET_MESSAGE_INTERVAL for RC_CHANNELS (message ID 65)
    {
        mavlink_message_t msg;
        mavlink_command_long_t cmd = {};

        cmd.target_system = config_.target_system_id;
        cmd.target_component = config_.target_component_id;
        cmd.command = MAV_CMD_SET_MESSAGE_INTERVAL;
        cmd.param1 = MAVLINK_MSG_ID_RC_CHANNELS;  // Message ID 65
        cmd.param2 = 100000;  // Interval in microseconds (100ms = 10Hz)
        cmd.param3 = 0;
        cmd.param4 = 0;
        cmd.param5 = 0;
        cmd.param6 = 0;
        cmd.param7 = 0;

        mavlink_msg_command_long_encode(config_.system_id, config_.component_id, &msg, &cmd);
        sendMessage(msg);

        if (config_.debug_messages) {
            std::cout << "Sent MAV_CMD_SET_MESSAGE_INTERVAL for RC_CHANNELS" << std::endl;
        }
    }

    // Method 2: MAV_CMD_SET_MESSAGE_INTERVAL for RC_CHANNELS_RAW (message ID 35)
    {
        mavlink_message_t msg;
        mavlink_command_long_t cmd = {};

        cmd.target_system = config_.target_system_id;
        cmd.target_component = config_.target_component_id;
        cmd.command = MAV_CMD_SET_MESSAGE_INTERVAL;
        cmd.param1 = MAVLINK_MSG_ID_RC_CHANNELS_RAW;  // Message ID 35
        cmd.param2 = 100000;  // Interval in microseconds (100ms = 10Hz)
        cmd.param3 = 0;
        cmd.param4 = 0;
        cmd.param5 = 0;
        cmd.param6 = 0;
        cmd.param7 = 0;

        mavlink_msg_command_long_encode(config_.system_id, config_.component_id, &msg, &cmd);
        sendMessage(msg);

        if (config_.debug_messages) {
            std::cout << "Sent MAV_CMD_SET_MESSAGE_INTERVAL for RC_CHANNELS_RAW" << std::endl;
        }
    }

    // Method 3: Legacy REQUEST_DATA_STREAM for MAV_DATA_STREAM_RC_CHANNELS
    {
        mavlink_message_t msg;
        mavlink_request_data_stream_t req = {};

        req.target_system = config_.target_system_id;
        req.target_component = config_.target_component_id;
        req.req_stream_id = MAV_DATA_STREAM_RC_CHANNELS;
        req.req_message_rate = 10;  // 10 Hz
        req.start_stop = 1;  // Start streaming

        mavlink_msg_request_data_stream_encode(config_.system_id, config_.component_id, &msg, &req);
        sendMessage(msg);

        if (config_.debug_messages) {
            std::cout << "Sent REQUEST_DATA_STREAM for RC_CHANNELS" << std::endl;
        }
    }
}

bool MAVLinkInterface::shouldRequestRCData() {
    // Don't request if we're already receiving RC data
    if (rc_data_received_.load()) {
        return false;
    }

    // Request every 2 seconds until we get RC data
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
        now - last_rc_request_time_).count();

    return elapsed >= 2;
}
