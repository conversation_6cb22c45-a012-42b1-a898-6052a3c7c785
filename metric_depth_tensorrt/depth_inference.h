#ifndef DEPTH_INFERENCE_H
#define DEPTH_INFERENCE_H

#include <opencv2/opencv.hpp>
#include <NvInfer.h>
#include <NvOnnxParser.h>
#include <cuda_runtime.h>
#include <memory>
#include <vector>
#include <string>
#include <chrono>
#include <yaml-cpp/yaml.h>
#include "camera.h"
#include "depth_strip_processor.h"
#include "radar_visualizer.h"
#include "depth_calibrator.h"
#include "mavlink_interface.h"

// TensorRT utilities
class Logger : public nvinfer1::ILogger {
public:
    void log(Severity severity, const char* msg) noexcept override;
};

// Configuration structure
struct Config {
    // Model configuration
    std::string engine_path;
    int input_size;
    float max_depth;
    std::string encoder;
    
    // Camera configuration
    int camera_sensor_id;
    int camera_width;
    int camera_height;
    int camera_fps;
    int camera_sensor_mode;
    int camera_flip_method;
    
    // Preprocessing configuration
    std::vector<float> mean;
    std::vector<float> std;
    bool keep_aspect_ratio;
    std::string resize_method;
    int ensure_multiple_of;
    std::string interpolation;
    
    // Postprocessing configuration
    bool output_original_size;
    std::string post_interpolation;
    
    // Visualization configuration
    bool debug;
    int window_width;
    int window_height;
    std::string colormap;
    bool show_fps;
    bool show_depth_range;
    
    // Performance configuration
    bool use_cuda_stream;
    int max_batch_size;
    size_t workspace_size;
    
    // Logging configuration
    std::string log_level;
    bool log_inference_time;
    bool log_preprocessing_time;
    bool log_postprocessing_time;
    
    // Output configuration
    bool save_depth_maps;
    std::string output_directory;
    std::string depth_format;
    bool save_rgb;
    
    // TensorRT configuration
    std::string precision;
    int dla_core;
    bool allow_gpu_fallback;

    // Depth calibration configuration
    DepthCalibrationConfig depth_calibration;

    // Depth strip processing configuration
    DepthStripConfig depth_strip;

    // Radar visualizer configuration
    RadarVisualizerConfig radar_visualizer;

    // MAVLink communication configuration
    MAVLinkConfig mavlink;

    // Load configuration from YAML file
    bool loadFromFile(const std::string& config_path);
};

// Main depth inference class
class DepthInference {
public:
    DepthInference();
    ~DepthInference();
    
    // Initialize the inference engine
    bool initialize(const std::string& config_path);
    
    // Run inference on camera feed
    void runCameraInference();
    
    // Run inference on single image
    cv::Mat inferImage(const cv::Mat& image);

    // Cleanup resources
    void cleanup();

    /**
     * @brief Start recording session
     */
    void startRecording();

    /**
     * @brief Stop recording session
     */
    void stopRecording();

    /**
     * @brief Save frame data during recording
     * @param frame_bgr Original BGR frame
     * @param depth_map Depth map
     * @param polar_points Polar coordinate data
     */
    void saveRecordingFrame(const cv::Mat& frame_bgr, const cv::Mat& depth_map,
                           const std::vector<PolarPoint>& polar_points);

private:
    // Configuration
    Config config_;
    
    // TensorRT components
    Logger logger_;
    nvinfer1::IRuntime* runtime_;
    nvinfer1::ICudaEngine* engine_;
    nvinfer1::IExecutionContext* context_;
    
    // CUDA components
    cudaStream_t stream_;
    void* gpu_input_buffer_;
    void* gpu_output_buffer_;
    void* cpu_input_buffer_;
    void* cpu_output_buffer_;
    
    // Model dimensions
    int input_height_;
    int input_width_;
    int input_channels_;
    int output_height_;
    int output_width_;
    size_t input_size_;
    size_t output_size_;
    
    // Camera
    realtime_depth_processor::camera::CSICameraInterface csi_camera_;

    // Depth calibrator
    DepthCalibrator depth_calibrator_;

    // Depth strip processor
    DepthStripProcessor depth_strip_processor_;

    // Radar visualizer
    RadarVisualizer radar_visualizer_;

    // MAVLink interface
    MAVLinkInterface mavlink_interface_;

    // Recording state
    bool recording_active_;
    std::string recording_session_dir_;
    uint32_t recording_frame_count_;
    std::chrono::high_resolution_clock::time_point recording_start_time_;

    // Timing
    std::chrono::high_resolution_clock::time_point last_time_;
    float fps_;
    
    // Private methods
    bool loadEngine(const std::string& engine_path);
    bool setupBuffers();
    cv::Mat preprocessImage(const cv::Mat& image, int& original_height, int& original_width);
    cv::Mat postprocessDepth(const float* depth_data, int original_height, int original_width);
    void visualizeDepth(const cv::Mat& rgb_image, const cv::Mat& depth_map);
    cv::Mat applyColormap(const cv::Mat& depth_map);
    void calculateFPS();
    void logTiming(const std::string& operation, double time_ms);
    
    // Resize utilities
    cv::Size calculateResizeSize(int original_width, int original_height);
    int constrainToMultipleOf(int value, int multiple, int min_val = 0, int max_val = -1);
    
    // CUDA error checking
    bool checkCudaError(cudaError_t error, const std::string& operation);
};

// Utility functions
std::string getCurrentTimestamp();
void createDirectoryIfNotExists(const std::string& path);

#endif // DEPTH_INFERENCE_H
