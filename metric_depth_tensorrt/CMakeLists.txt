cmake_minimum_required(VERSION 3.12)
project(orbit)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)

# Add source files
set(SOURCES
    main.cpp
    depth_inference.cpp
    camera.cpp
    depth_calibrator.cpp
    depth_strip_processor.cpp
    radar_visualizer.cpp
    mavlink_interface.cpp
)

# Add headers
set(HEADERS
    depth_inference.h
    camera.h
    depth_calibrator.h
    depth_strip_processor.h
    radar_visualizer.h
    mavlink_interface.h
)

# ================= CUDA =================
find_package(CUDA REQUIRED)
include_directories(${CUDA_INCLUDE_DIRS})

# ================= OpenCV =================
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

# ================= yaml-cpp =================
find_package(PkgConfig REQUIRED)
pkg_check_modules(YAML_CPP REQUIRED yaml-cpp)

# ================= MAVLink =================
# MAVLink headers (download if not present)
set(MAVLINK_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/mavlink_c_library_v2")
if(NOT EXISTS ${MAVLINK_INCLUDE_DIR})
    message(STATUS "Downloading MAVLink C library...")
    execute_process(
        COMMAND git clone https://github.com/mavlink/c_library_v2.git mavlink_c_library_v2
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        RESULT_VARIABLE GIT_RESULT
    )
    if(NOT GIT_RESULT EQUAL "0")
        message(FATAL_ERROR "Failed to download MAVLink C library")
    endif()
endif()
include_directories(${MAVLINK_INCLUDE_DIR})
include_directories(${MAVLINK_INCLUDE_DIR}/common)
message(STATUS "MAVLink headers: ${MAVLINK_INCLUDE_DIR}")

# ================= TensorRT =================
set(TensorRT_INCLUDE "/usr/include/aarch64-linux-gnu" CACHE INTERNAL "TensorRT include location")
set(TensorRT_LIB "/usr/lib/aarch64-linux-gnu" CACHE INTERNAL "TensorRT lib location")
include_directories(${TensorRT_INCLUDE})

# Detect TensorRT version
set(TENSORRT_VERSION_FILE "/usr/include/aarch64-linux-gnu/NvInferVersion.h")
if(NOT EXISTS ${TENSORRT_VERSION_FILE})
    set(TENSORRT_VERSION_FILE "/usr/include/NvInferVersion.h")
endif()

if(EXISTS ${TENSORRT_VERSION_FILE})
    file(STRINGS ${TENSORRT_VERSION_FILE} tensorrt_version REGEX "#define NV_TENSORRT_MAJOR +[0-9]+")
    string(REGEX MATCH "[0-9]+" tensorrt_version_major ${tensorrt_version})

    if (tensorrt_version_major EQUAL 10)
        set(TENSORRT_LIBS nvinfer nvinfer_plugin nvonnxparser)
    else()
        set(TENSORRT_LIBS nvinfer nvinfer_plugin nvparsers nvonnxparser)
    endif()
    message(STATUS "Found TensorRT version: ${tensorrt_version_major}")
else()
    message(WARNING "TensorRT version file not found, using default libs")
    set(TENSORRT_LIBS nvinfer nvinfer_plugin nvonnxparser)
endif()

# ================= Executable =================
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})
add_executable(calibrate_depth calibrate_depth.cpp depth_calibrator.cpp depth_calibrator.h)

# Set output name to orbit
set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME "orbit")

# Link libraries
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBS}
    ${CUDA_LIBRARIES}
    ${TENSORRT_LIBS}
    ${YAML_CPP_LIBRARIES}
)
target_link_libraries(calibrate_depth
    ${OpenCV_LIBS}
    ${YAML_CPP_LIBRARIES}
)

# MAVLink is header-only, no additional libraries needed
message(STATUS "MAVLink enabled")
