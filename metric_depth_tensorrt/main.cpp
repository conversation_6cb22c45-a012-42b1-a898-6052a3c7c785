#include "depth_inference.h"
#include <iostream>
#include <string>

int main(int argc, char* argv[]) {
    std::string config_path = "config.yaml";
    
    if (argc > 1) {
        config_path = argv[1];
    }
    
    std::cout << "=== Depth Anything V2 TensorRT Inference ===" << std::endl;
    std::cout << "Using config: " << config_path << std::endl;
    std::cout << "TensorRT version: " << NV_TENSORRT_MAJOR << "." << NV_TENSORRT_MINOR << std::endl;
    
    DepthInference depth_inference;
    
    if (!depth_inference.initialize(config_path)) {
        std::cerr << "Failed to initialize depth inference" << std::endl;
        return -1;
    }
    
    try {
        depth_inference.runCameraInference();
    } catch (const std::exception& e) {
        std::cerr << "Error during inference: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
