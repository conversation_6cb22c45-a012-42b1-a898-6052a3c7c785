#pragma once

#include <string>
#include <vector>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>
#include <functional>
#include <yaml-cpp/yaml.h>
#include <mavlink.h>
#include "depth_strip_processor.h"

/**
 * @brief MAVLink communication configuration
 */
struct MAVLinkConfig {
    bool enabled;                    // Enable MAVLink communication
    std::string serial_port;         // Serial port path
    int baudrate;                    // Baudrate
    uint8_t system_id;              // Our system ID
    uint8_t component_id;           // Our component ID
    uint8_t target_system_id;       // Target system ID (autopilot)
    uint8_t target_component_id;    // Target component ID
    float heartbeat_rate_hz;        // Heartbeat transmission rate
    float obstacle_distance_rate_hz; // OBSTACLE_DISTANCE message rate
    bool debug_messages;            // Enable debug message printing
    
    // OBSTACLE_DISTANCE configuration
    float min_distance;             // Minimum distance in meters
    float max_distance;             // Maximum distance in meters
    float increment_deg;            // Angular increment in degrees

    // Recording control via RC channel
    uint8_t recording_channel;      // RC channel number for recording control (1-18)
    uint16_t recording_pwm_threshold; // PWM threshold for recording
    uint16_t recording_pwm_high;    // PWM value considered "high" (recording ON)
    uint16_t recording_pwm_low;     // PWM value considered "low" (recording OFF)

    // Distance data disable control via RC channel
    uint8_t disable_channel;        // RC channel number for disabling distance data (1-18)
    uint16_t disable_pwm_threshold; // PWM threshold for disabling
    uint16_t disable_pwm_high;      // PWM value considered "high" (distance DISABLED)
    uint16_t disable_pwm_low;       // PWM value considered "low" (distance ENABLED)
    
    // Default values
    MAVLinkConfig()
        : enabled(false)
        , serial_port("/dev/ttyTHS0")
        , baudrate(115200)
        , system_id(1)
        , component_id(158) // MAV_COMP_ID_PERIPHERAL
        , target_system_id(1)
        , target_component_id(1)
        , heartbeat_rate_hz(15.0f)
        , obstacle_distance_rate_hz(5.0f)
        , debug_messages(false)
        , min_distance(0.2f)
        , max_distance(80.0f)
        , increment_deg(5.0f)
        , recording_channel(16)
        , recording_pwm_threshold(1500)
        , recording_pwm_high(1800)
        , recording_pwm_low(1200)
        , disable_channel(10)
        , disable_pwm_threshold(1500)
        , disable_pwm_high(1800)
        , disable_pwm_low(1200) {}
};

/**
 * @brief Structure to hold received autopilot data
 */
struct AutopilotData {
    bool connected;
    bool armed;
    uint8_t autopilot_type;
    uint8_t system_status;
    std::chrono::high_resolution_clock::time_point last_heartbeat_time;
    uint32_t received_heartbeats;

    // RC channel data
    uint16_t rc_channels[18];       // RC channel values (PWM 1000-2000)
    bool rc_channels_valid;         // True if RC data is available
    bool recording_active;          // True if recording should be active based on RC
    bool distance_disabled;         // True if distance data should be disabled based on RC

    AutopilotData()
        : connected(false)
        , armed(false)
        , autopilot_type(MAV_AUTOPILOT_INVALID)
        , system_status(MAV_STATE_UNINIT)
        , received_heartbeats(0)
        , rc_channels_valid(false)
        , recording_active(false)
        , distance_disabled(false) {
        // Initialize RC channels to neutral (1500)
        for (int i = 0; i < 18; ++i) {
            rc_channels[i] = 1500;
        }
    }
};

/**
 * @brief Simple, fast MAVLink interface class
 * Based on proven companion computer implementation
 */
class MAVLinkInterface {
public:
    MAVLinkInterface();
    ~MAVLinkInterface();
    
    /**
     * @brief Initialize MAVLink interface
     * @param config Configuration structure
     * @return true if initialization successful
     */
    bool initialize(const MAVLinkConfig& config);
    
    /**
     * @brief Load configuration from YAML node
     * @param config_node YAML node containing mavlink configuration
     * @return true if configuration loaded successfully
     */
    bool loadConfig(const YAML::Node& config_node);
    
    /**
     * @brief Start MAVLink communication
     * @return true if started successfully
     */
    bool start();
    
    /**
     * @brief Stop MAVLink communication
     */
    void stop();
    
    /**
     * @brief Check if interface is enabled
     * @return true if enabled
     */
    bool isEnabled() const { return config_.enabled; }
    
    /**
     * @brief Check if connected to autopilot
     * @return true if connected
     */
    bool isConnected() const;
    
    /**
     * @brief Send OBSTACLE_DISTANCE message from polar points
     * @param polar_points Vector of polar coordinate points
     * @return true if sent successfully
     */
    bool sendObstacleDistanceFromPolar(const std::vector<PolarPoint>& polar_points);
    
    /**
     * @brief Send OBSTACLE_DISTANCE message
     * @param distances Array of distances in meters (72 elements for 5° increments)
     * @param angle_offset Starting angle offset in degrees
     * @return true if sent successfully
     */
    bool sendObstacleDistance(const float* distances, float angle_offset = 0.0f);
    
    /**
     * @brief Get current autopilot data
     * @return Autopilot data structure
     */
    AutopilotData getAutopilotData() const;

    /**
     * @brief Check if recording should be active based on RC channel
     * @return true if recording should be active
     */
    bool isRecordingActive() const;

    /**
     * @brief Check if distance data is currently disabled based on RC channel
     * @return true if distance data should be disabled (send max+1 values)
     */
    bool isDistanceDisabled() const;
    
    /**
     * @brief Set message callback for received messages
     * @param callback Function to call when message received
     */
    void setMessageCallback(std::function<void(const mavlink_message_t&)> callback);
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    const MAVLinkConfig& getConfig() const { return config_; }

private:
    MAVLinkConfig config_;
    bool initialized_;
    std::atomic<bool> running_;
    
    // Serial communication
    int serial_fd_;
    
    // Threading
    std::thread read_thread_;
    std::thread heartbeat_thread_;
    
    // Status tracking
    mutable std::mutex status_mutex_;
    AutopilotData autopilot_data_;
    std::atomic<bool> autopilot_detected_;
    
    // Timing
    std::chrono::high_resolution_clock::time_point last_heartbeat_sent_time_;
    std::chrono::high_resolution_clock::time_point last_obstacle_distance_time_;
    
    // Statistics
    std::atomic<uint32_t> sent_heartbeats_;
    std::atomic<uint32_t> sent_obstacles_;

    // RC data stream requesting
    std::atomic<bool> rc_data_received_;
    std::chrono::high_resolution_clock::time_point last_rc_request_time_;
    
    // Message callback
    std::mutex callback_mutex_;
    std::function<void(const mavlink_message_t&)> message_callback_;
    
    /**
     * @brief Setup serial port
     * @return true if successful
     */
    bool setupSerialPort();
    
    /**
     * @brief Read thread function
     */
    void readThreadFunction();
    
    /**
     * @brief Heartbeat thread function
     */
    void heartbeatThreadFunction();
    
    /**
     * @brief Send MAVLink message via serial
     * @param msg Message to send
     */
    void sendMessage(const mavlink_message_t& msg);
    
    /**
     * @brief Handle received MAVLink message
     * @param msg Received message
     */
    void handleMessage(const mavlink_message_t& msg);
    
    /**
     * @brief Send heartbeat message
     */
    void sendHeartbeat();

    /**
     * @brief Request RC channel data streams from autopilot
     */
    void requestRCDataStreams();

    /**
     * @brief Check if RC data stream request should be sent
     * @return true if should request
     */
    bool shouldRequestRCData();
    
    /**
     * @brief Convert polar points to obstacle distance array
     * @param polar_points Input polar points
     * @param distances Output distance array (72 elements)
     * @param angle_offset Starting angle offset
     */
    void convertPolarToObstacleArray(const std::vector<PolarPoint>& polar_points,
                                   float* distances, float angle_offset = 0.0f);
    
    /**
     * @brief Check if it's time to send obstacle distance
     * @return true if should send
     */
    bool shouldSendObstacleDistance();
    
    /**
     * @brief Get current time in microseconds
     * @return Time in microseconds
     */
    uint64_t getCurrentTimeUsec();
    
    /**
     * @brief Check connection status based on received heartbeats
     * @return true if connected
     */
    bool checkConnectionStatus() const;
};
