#include "radar_visualizer.h"
#include <iostream>
#include <iomanip>
#include <cmath>
#include <chrono>
#include <filesystem>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

RadarVisualizer::RadarVisualizer() : initialized_(false) {
}

RadarVisualizer::~RadarVisualizer() {
    if (initialized_) {
        cv::destroyWindow("Radar Display");
    }
}

bool RadarVisualizer::initialize(const RadarVisualizerConfig& config) {
    config_ = config;
    
    if (!config_.enabled) {
        initialized_ = true;
        return true;
    }
    
    // Calculate center point and scaling
    center_point_ = cv::Point2f(config_.window_width / 2.0f, config_.window_height / 2.0f);
    
    // Calculate pixels per meter (use smaller dimension to ensure radar fits)
    float max_radius_pixels = std::min(config_.window_width, config_.window_height) / 2.0f - 50; // 50 pixel margin
    pixels_per_meter_ = max_radius_pixels / config_.max_radius_meters;
    
    // Create output directory if saving images
    if (config_.save_radar_images) {
        std::filesystem::create_directories(config_.output_directory);
    }
    
    // Create base radar display
    createBaseRadar();
    
    // Create window
    cv::namedWindow("Radar Display", cv::WINDOW_AUTOSIZE);
    
    initialized_ = true;
    last_update_time_ = std::chrono::high_resolution_clock::now();
    
    std::cout << "RadarVisualizer initialized:" << std::endl;
    std::cout << "  Enabled: " << (config_.enabled ? "true" : "false") << std::endl;
    std::cout << "  Max radius: " << config_.max_radius_meters << " meters" << std::endl;
    std::cout << "  Window size: " << config_.window_width << "x" << config_.window_height << std::endl;
    std::cout << "  Pixels per meter: " << pixels_per_meter_ << std::endl;
    std::cout << "  Update rate: " << config_.update_rate_hz << " Hz" << std::endl;
    
    return true;
}

bool RadarVisualizer::loadConfig(const YAML::Node& config_node) {
    try {
        if (!config_node) {
            std::cout << "No radar_visualizer configuration found, using defaults (disabled)" << std::endl;
            config_.enabled = false;
            return true;
        }
        
        config_.enabled = config_node["enabled"].as<bool>(false);
        config_.max_radius_meters = config_node["max_radius_meters"].as<float>(80.0f);
        config_.window_width = config_node["window_width"].as<int>(800);
        config_.window_height = config_node["window_height"].as<int>(800);
        config_.show_grid = config_node["show_grid"].as<bool>(true);
        config_.show_range_rings = config_node["show_range_rings"].as<bool>(true);
        config_.show_angle_lines = config_node["show_angle_lines"].as<bool>(true);
        config_.grid_divisions = config_node["grid_divisions"].as<int>(8);
        config_.point_size = config_node["point_size"].as<float>(3.0f);
        config_.save_radar_images = config_node["save_radar_images"].as<bool>(false);
        config_.output_directory = config_node["output_directory"].as<std::string>("radar_output");
        config_.show_distance_labels = config_node["show_distance_labels"].as<bool>(true);
        config_.show_angle_labels = config_node["show_angle_labels"].as<bool>(true);
        config_.update_rate_hz = config_node["update_rate_hz"].as<float>(10.0f);
        
        // Parse colors if provided
        if (config_node["background_color"]) {
            auto bg = config_node["background_color"];
            config_.background_color = cv::Scalar(bg[0].as<int>(), bg[1].as<int>(), bg[2].as<int>());
        }
        if (config_node["object_color"]) {
            auto obj = config_node["object_color"];
            config_.object_color = cv::Scalar(obj[0].as<int>(), obj[1].as<int>(), obj[2].as<int>());
        }
        
        return initialize(config_);
    } catch (const std::exception& e) {
        std::cerr << "Error loading radar visualizer configuration: " << e.what() << std::endl;
        config_.enabled = false;
        return false;
    }
}

void RadarVisualizer::update(const std::vector<PolarPoint>& polar_points, int frame_id) {
    if (!initialized_ || !config_.enabled) {
        return;
    }
    
    // Check update rate limiting
    if (!shouldUpdate()) {
        return;
    }
    
    // Create fresh radar image
    createBaseRadar();
    
    // Draw objects
    drawObjects(polar_points);
    
    // Save image if requested
    if (config_.save_radar_images && frame_id >= 0) {
        std::string filename = config_.output_directory + "/radar_frame_" + 
                              std::to_string(frame_id) + ".png";
        saveImage(filename);
    }
    
    last_update_time_ = std::chrono::high_resolution_clock::now();
}

int RadarVisualizer::show(int wait_key_ms) {
    if (!initialized_ || !config_.enabled || radar_image_.empty()) {
        return -1;
    }
    
    cv::imshow("Radar Display", radar_image_);
    return cv::waitKey(wait_key_ms) & 0xFF;
}

void RadarVisualizer::saveImage(const std::string& filename) {
    if (!radar_image_.empty()) {
        cv::imwrite(filename, radar_image_);
    }
}

void RadarVisualizer::createBaseRadar() {
    // Create blank image
    radar_image_ = cv::Mat::zeros(config_.window_height, config_.window_width, CV_8UC3);
    radar_image_.setTo(config_.background_color);
    
    // Draw radar elements
    if (config_.show_range_rings) {
        drawRangeRings();
    }
    
    if (config_.show_angle_lines) {
        drawAngleLines();
    }
    
    if (config_.show_grid) {
        drawGrid();
    }
    
    if (config_.show_distance_labels) {
        drawDistanceLabels();
    }
    
    if (config_.show_angle_labels) {
        drawAngleLabels();
    }
    
    // Draw center point
    cv::circle(radar_image_, center_point_, 3, config_.center_color, -1);
}

void RadarVisualizer::drawRangeRings() {
    float ring_spacing = config_.max_radius_meters / config_.grid_divisions;
    
    for (int i = 1; i <= config_.grid_divisions; ++i) {
        float radius_meters = i * ring_spacing;
        int radius_pixels = static_cast<int>(radius_meters * pixels_per_meter_);
        
        cv::circle(radar_image_, center_point_, radius_pixels, config_.range_ring_color, 1);
    }
}

void RadarVisualizer::drawAngleLines() {
    float max_radius_pixels = config_.max_radius_meters * pixels_per_meter_;
    
    // Draw lines every 30 degrees
    for (int angle = 0; angle < 360; angle += 30) {
        float angle_rad = angle * M_PI / 180.0f;
        
        cv::Point2f end_point(
            center_point_.x + max_radius_pixels * std::sin(angle_rad),
            center_point_.y - max_radius_pixels * std::cos(angle_rad)
        );
        
        cv::line(radar_image_, center_point_, end_point, config_.angle_line_color, 1);
    }
}

void RadarVisualizer::drawGrid() {
    // Grid is combination of range rings and angle lines
    // Already drawn by drawRangeRings() and drawAngleLines()
}

void RadarVisualizer::drawDistanceLabels() {
    float ring_spacing = config_.max_radius_meters / config_.grid_divisions;
    
    for (int i = 1; i <= config_.grid_divisions; ++i) {
        float radius_meters = i * ring_spacing;
        int radius_pixels = static_cast<int>(radius_meters * pixels_per_meter_);
        
        // Position label at top of ring
        cv::Point2f label_pos(center_point_.x + 5, center_point_.y - radius_pixels);
        
        std::string label = std::to_string(static_cast<int>(radius_meters)) + "m";
        cv::putText(radar_image_, label, label_pos, cv::FONT_HERSHEY_SIMPLEX, 
                   0.4, config_.range_ring_color, 1);
    }
}

void RadarVisualizer::drawAngleLabels() {
    float label_radius_pixels = config_.max_radius_meters * pixels_per_meter_ + 20;
    
    // Draw angle labels every 30 degrees
    for (int angle = 0; angle < 360; angle += 30) {
        float angle_rad = angle * M_PI / 180.0f;
        
        cv::Point2f label_pos(
            center_point_.x + label_radius_pixels * std::sin(angle_rad) - 10,
            center_point_.y - label_radius_pixels * std::cos(angle_rad) + 5
        );
        
        std::string label = std::to_string(angle) + "°";
        cv::putText(radar_image_, label, label_pos, cv::FONT_HERSHEY_SIMPLEX, 
                   0.4, config_.angle_line_color, 1);
    }
}

cv::Point2f RadarVisualizer::polarToPixel(float yaw_degrees, float distance_meters) {
    // Convert yaw to standard mathematical angle (0° = up, positive = clockwise)
    float angle_rad = yaw_degrees * M_PI / 180.0f;
    
    // Calculate pixel position
    float x = center_point_.x + distance_meters * pixels_per_meter_ * std::sin(angle_rad);
    float y = center_point_.y - distance_meters * pixels_per_meter_ * std::cos(angle_rad);
    
    return cv::Point2f(x, y);
}

void RadarVisualizer::drawObjects(const std::vector<PolarPoint>& polar_points) {
    for (const auto& point : polar_points) {
        // Skip points outside radar range
        if (point.distance > config_.max_radius_meters || point.distance <= 0) {
            continue;
        }
        
        cv::Point2f pixel_pos = polarToPixel(point.yaw_degrees, point.distance);
        
        // Check if point is within image bounds
        if (pixel_pos.x >= 0 && pixel_pos.x < config_.window_width &&
            pixel_pos.y >= 0 && pixel_pos.y < config_.window_height) {
            
            cv::circle(radar_image_, pixel_pos, config_.point_size, config_.object_color, -1);
        }
    }
}

bool RadarVisualizer::shouldUpdate() {
    if (config_.update_rate_hz <= 0) {
        return true; // No rate limiting
    }
    
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration<double>(now - last_update_time_).count();
    double min_interval = 1.0 / config_.update_rate_hz;
    
    return elapsed >= min_interval;
}
