#include "depth_strip_processor.h"
#include <iostream>
#include <fstream>
#include <iomanip>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

DepthStripProcessor::DepthStripProcessor() : initialized_(false) {
}

DepthStripProcessor::~DepthStripProcessor() {
}

bool DepthStripProcessor::initialize(const DepthStripConfig& config) {
    config_ = config;
    initialized_ = true;
    
    if (config_.enabled) {
        std::cout << "DepthStripProcessor initialized:" << std::endl;
        std::cout << "  Enabled: " << (config_.enabled ? "true" : "false") << std::endl;
        std::cout << "  Horizontal FOV: " << config_.horizontal_fov_degrees << " degrees" << std::endl;
        std::cout << "  Strip height: " << config_.strip_height << " pixels" << std::endl;
        std::cout << "  Gaussian sigma factor: " << config_.gaussian_sigma_factor << std::endl;
        std::cout << "  Save polar data: " << (config_.save_polar_data ? "true" : "false") << std::endl;
        if (config_.save_polar_data) {
            std::cout << "  Output file: " << config_.output_file << std::endl;
        }
    }
    
    return true;
}

bool DepthStripProcessor::loadConfig(const YAML::Node& config_node) {
    try {
        if (!config_node) {
            std::cout << "No depth_strip configuration found, using defaults (disabled)" << std::endl;
            config_.enabled = false;
            return true;
        }
        
        config_.enabled = config_node["enabled"].as<bool>(false);
        config_.horizontal_fov_degrees = config_node["horizontal_fov_degrees"].as<float>(73.0f);
        config_.strip_height = config_node["strip_height"].as<int>(50);
        config_.gaussian_sigma_factor = config_node["gaussian_sigma_factor"].as<float>(6.0f);
        config_.save_polar_data = config_node["save_polar_data"].as<bool>(false);
        config_.output_file = config_node["output_file"].as<std::string>("polar_coordinates.csv");
        
        return initialize(config_);
    } catch (const std::exception& e) {
        std::cerr << "Error loading depth strip configuration: " << e.what() << std::endl;
        config_.enabled = false;
        return false;
    }
}

std::vector<PolarPoint> DepthStripProcessor::process(const cv::Mat& depth_map) {
    if (!initialized_ || !config_.enabled) {
        return std::vector<PolarPoint>();
    }
    
    if (depth_map.empty() || depth_map.type() != CV_32F) {
        std::cerr << "Invalid depth map for strip processing" << std::endl;
        return std::vector<PolarPoint>();
    }
    
    // Extract horizontal strip from center of depth map
    cv::Mat strip = extractHorizontalStrip(depth_map, config_.strip_height);
    
    // Apply weighted vertical averaging
    std::vector<float> averaged_depths = applyWeightedVerticalAveraging(strip);
    
    // Convert to polar coordinates
    std::vector<PolarPoint> polar_points = convertToPolarCoordinates(averaged_depths, depth_map.cols);
    
    // Save to file if requested
    if (config_.save_polar_data && !polar_points.empty()) {
        savePolarDataToCSV(polar_points, config_.output_file);
    }
    
    return polar_points;
}

cv::Mat DepthStripProcessor::extractHorizontalStrip(const cv::Mat& depth_map, int strip_height) {
    int image_height = depth_map.rows;
    int image_width = depth_map.cols;
    
    // Calculate center row and strip boundaries
    int center_row = image_height / 2;
    int start_row = center_row - strip_height / 2;
    int end_row = start_row + strip_height;
    
    // Ensure boundaries are within image
    start_row = std::max(0, start_row);
    end_row = std::min(image_height, end_row);
    
    // Extract the strip
    cv::Rect strip_rect(0, start_row, image_width, end_row - start_row);
    return depth_map(strip_rect).clone();
}

std::vector<float> DepthStripProcessor::applyWeightedVerticalAveraging(const cv::Mat& strip) {
    int strip_height = strip.rows;
    int strip_width = strip.cols;
    std::vector<float> averaged_depths(strip_width, 0.0f);
    
    // Create Gaussian weights for vertical averaging (more weight to center pixels)
    std::vector<float> weights(strip_height);
    float center = (strip_height - 1) / 2.0f;
    float sigma = strip_height / config_.gaussian_sigma_factor;
    float weight_sum = 0.0f;
    
    for (int i = 0; i < strip_height; ++i) {
        float distance_from_center = std::abs(i - center);
        weights[i] = std::exp(-(distance_from_center * distance_from_center) / (2.0f * sigma * sigma));
        weight_sum += weights[i];
    }
    
    // Normalize weights
    for (int i = 0; i < strip_height; ++i) {
        weights[i] /= weight_sum;
    }
    
    // Apply weighted averaging for each column
    for (int col = 0; col < strip_width; ++col) {
        float weighted_sum = 0.0f;
        
        for (int row = 0; row < strip_height; ++row) {
            float depth_value = strip.at<float>(row, col);
            weighted_sum += depth_value * weights[row];
        }
        
        averaged_depths[col] = weighted_sum;
    }
    
    return averaged_depths;
}

std::vector<PolarPoint> DepthStripProcessor::convertToPolarCoordinates(const std::vector<float>& averaged_depths,
                                                                       int image_width) {
    int width = averaged_depths.size();
    std::vector<PolarPoint> polar_points(width);
    
    // Convert FOV to radians
    float fov_radians = config_.horizontal_fov_degrees * M_PI / 180.0f;
    float half_fov = fov_radians / 2.0f;
    
    // Calculate center pixel based on actual image width (for proper scaling with different resolutions)
    float center_pixel = (image_width - 1) / 2.0f;
    float pixels_per_radian = center_pixel / half_fov;
    
    for (int i = 0; i < width; ++i) {
        // Calculate pixel offset from center
        float pixel_offset = i - center_pixel;
        
        // Convert pixel offset to angle
        // Negative for left side, positive for right side, 0 for center
        float yaw_radians = pixel_offset / pixels_per_radian;
        float yaw_degrees = yaw_radians * 180.0f / M_PI;
        
        // Get distance (depth value)
        float distance = averaged_depths[i];
        
        // Create polar point
        polar_points[i] = PolarPoint(yaw_degrees, distance);
    }
    
    return polar_points;
}

void DepthStripProcessor::savePolarDataToCSV(const std::vector<PolarPoint>& polar_points, 
                                             const std::string& filename) {
    std::ofstream csv_file(filename);
    if (!csv_file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return;
    }
    
    csv_file << "pixel,yaw_degrees,distance_meters\n";
    
    for (size_t i = 0; i < polar_points.size(); ++i) {
        csv_file << i << "," << std::fixed << std::setprecision(6) << polar_points[i].yaw_degrees 
                 << "," << std::setprecision(6) << polar_points[i].distance << "\n";
    }
    
    csv_file.close();
    std::cout << "Polar coordinate data saved to: " << filename << std::endl;
}

void DepthStripProcessor::printPolarData(const std::vector<PolarPoint>& polar_points, int sample_step) {
    if (polar_points.empty()) {
        std::cout << "No polar points to display" << std::endl;
        return;
    }
    
    std::cout << "\n=== Polar Coordinate Data (sampled every " << sample_step << " pixels) ===" << std::endl;
    std::cout << "Pixel\tYaw (deg)\tDistance (m)" << std::endl;
    std::cout << "-----\t---------\t-----------" << std::endl;
    
    for (size_t i = 0; i < polar_points.size(); i += sample_step) {
        std::cout << std::setw(5) << i 
                  << "\t" << std::setw(9) << std::fixed << std::setprecision(2) << polar_points[i].yaw_degrees
                  << "\t" << std::setw(11) << std::fixed << std::setprecision(3) << polar_points[i].distance
                  << std::endl;
    }
    
    // Show range information
    std::cout << "\nRange: " << std::fixed << std::setprecision(2) 
              << polar_points.front().yaw_degrees << "° to " 
              << polar_points.back().yaw_degrees << "° (" 
              << polar_points.size() << " points)" << std::endl;
}
