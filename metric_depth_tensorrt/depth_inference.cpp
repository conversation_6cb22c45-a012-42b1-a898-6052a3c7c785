#include "depth_inference.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <NvOnnxParser.h>

using namespace nvinfer1;

// Logger implementation
void Logger::log(Severity severity, const char* msg) noexcept {
    std::string level;
    switch (severity) {
        case Severity::kINTERNAL_ERROR: level = "INTERNAL_ERROR"; break;
        case Severity::kERROR: level = "ERROR"; break;
        case Severity::kWARNING: level = "WARNING"; break;
        case Severity::kINFO: level = "INFO"; break;
        case Severity::kVERBOSE: level = "VERBOSE"; break;
        default: level = "UNKNOWN"; break;
    }
    std::cout << "[TensorRT " << level << "] " << msg << std::endl;
}

// Config implementation
bool Config::loadFromFile(const std::string& config_path) {
    try {
        YAML::Node config = YAML::LoadFile(config_path);
        
        // Model configuration
        engine_path = config["model"]["engine_path"].as<std::string>();
        input_size = config["model"]["input_size"].as<int>();
        max_depth = config["model"]["max_depth"].as<float>();
        encoder = config["model"]["encoder"].as<std::string>();
        
        // Camera configuration
        camera_sensor_id = config["camera"]["sensor_id"].as<int>();
        camera_width = config["camera"]["width"].as<int>();
        camera_height = config["camera"]["height"].as<int>();
        camera_fps = config["camera"]["fps"].as<int>();
        camera_sensor_mode = config["camera"]["sensor_mode"].as<int>();
        camera_flip_method = config["camera"]["flip_method"].as<int>();
        
        // Preprocessing configuration
        auto mean_node = config["preprocessing"]["mean"];
        mean = {mean_node[0].as<float>(), mean_node[1].as<float>(), mean_node[2].as<float>()};
        auto std_node = config["preprocessing"]["std"];
        std = {std_node[0].as<float>(), std_node[1].as<float>(), std_node[2].as<float>()};
        keep_aspect_ratio = config["preprocessing"]["keep_aspect_ratio"].as<bool>();
        resize_method = config["preprocessing"]["resize_method"].as<std::string>();
        ensure_multiple_of = config["preprocessing"]["ensure_multiple_of"].as<int>();
        interpolation = config["preprocessing"]["interpolation"].as<std::string>();
        
        // Postprocessing configuration
        output_original_size = config["postprocessing"]["output_original_size"].as<bool>();
        post_interpolation = config["postprocessing"]["interpolation"].as<std::string>();
        
        // Visualization configuration
        debug = config["visualization"]["debug"].as<bool>();
        window_width = config["visualization"]["window_width"].as<int>();
        window_height = config["visualization"]["window_height"].as<int>();
        colormap = config["visualization"]["colormap"].as<std::string>();
        show_fps = config["visualization"]["show_fps"].as<bool>();
        show_depth_range = config["visualization"]["show_depth_range"].as<bool>();
        
        // Performance configuration
        use_cuda_stream = config["performance"]["use_cuda_stream"].as<bool>();
        max_batch_size = config["performance"]["max_batch_size"].as<int>();
        workspace_size = config["performance"]["workspace_size"].as<size_t>();
        
        // Logging configuration
        log_level = config["logging"]["level"].as<std::string>();
        log_inference_time = config["logging"]["log_inference_time"].as<bool>();
        log_preprocessing_time = config["logging"]["log_preprocessing_time"].as<bool>();
        log_postprocessing_time = config["logging"]["log_postprocessing_time"].as<bool>();
        
        // Output configuration
        save_depth_maps = config["output"]["save_depth_maps"].as<bool>();
        output_directory = config["output"]["output_directory"].as<std::string>();
        depth_format = config["output"]["depth_format"].as<std::string>();
        save_rgb = config["output"]["save_rgb"].as<bool>();
        
        // TensorRT configuration
        precision = config["tensorrt"]["precision"].as<std::string>();
        dla_core = config["tensorrt"]["dla_core"].as<int>();
        allow_gpu_fallback = config["tensorrt"]["allow_gpu_fallback"].as<bool>();

        // Depth calibration configuration
        if (config["depth_calibration"]) {
            depth_calibration.enabled = config["depth_calibration"]["enabled"].as<bool>(false);
            depth_calibration.calibration_file = config["depth_calibration"]["calibration_file"].as<std::string>("calibration_data/camera_0_calib.xml");
            depth_calibration.focal_length_scale_factor = config["depth_calibration"]["focal_length_scale_factor"].as<float>(1.0f);
            depth_calibration.depth_offset_meters = config["depth_calibration"]["depth_offset_meters"].as<float>(0.0f);
            depth_calibration.depth_scale_factor = config["depth_calibration"]["depth_scale_factor"].as<float>(1.0f);
            depth_calibration.use_focal_length_correction = config["depth_calibration"]["use_focal_length_correction"].as<bool>(true);
            depth_calibration.use_manual_correction = config["depth_calibration"]["use_manual_correction"].as<bool>(false);
            depth_calibration.model_fx = config["depth_calibration"]["model_fx"].as<double>(725.0087);
            depth_calibration.model_fy = config["depth_calibration"]["model_fy"].as<double>(725.0087);
            depth_calibration.model_width = config["depth_calibration"]["model_width"].as<int>(1241);
            depth_calibration.model_height = config["depth_calibration"]["model_height"].as<int>(374);
        } else {
            depth_calibration.enabled = false;
        }

        // Depth strip configuration
        if (config["depth_strip"]) {
            depth_strip.enabled = config["depth_strip"]["enabled"].as<bool>(false);
            depth_strip.horizontal_fov_degrees = config["depth_strip"]["horizontal_fov_degrees"].as<float>(73.0f);
            depth_strip.strip_height = config["depth_strip"]["strip_height"].as<int>(50);
            depth_strip.gaussian_sigma_factor = config["depth_strip"]["gaussian_sigma_factor"].as<float>(6.0f);
            depth_strip.save_polar_data = config["depth_strip"]["save_polar_data"].as<bool>(false);
            depth_strip.output_file = config["depth_strip"]["output_file"].as<std::string>("polar_coordinates.csv");
        } else {
            depth_strip.enabled = false;
        }

        // Radar visualizer configuration
        if (config["radar_visualizer"]) {
            radar_visualizer.enabled = config["radar_visualizer"]["enabled"].as<bool>(false);
            radar_visualizer.max_radius_meters = config["radar_visualizer"]["max_radius_meters"].as<float>(80.0f);
            radar_visualizer.window_width = config["radar_visualizer"]["window_width"].as<int>(800);
            radar_visualizer.window_height = config["radar_visualizer"]["window_height"].as<int>(800);
            radar_visualizer.show_grid = config["radar_visualizer"]["show_grid"].as<bool>(true);
            radar_visualizer.show_range_rings = config["radar_visualizer"]["show_range_rings"].as<bool>(true);
            radar_visualizer.show_angle_lines = config["radar_visualizer"]["show_angle_lines"].as<bool>(true);
            radar_visualizer.grid_divisions = config["radar_visualizer"]["grid_divisions"].as<int>(8);
            radar_visualizer.point_size = config["radar_visualizer"]["point_size"].as<float>(3.0f);
            radar_visualizer.save_radar_images = config["radar_visualizer"]["save_radar_images"].as<bool>(false);
            radar_visualizer.output_directory = config["radar_visualizer"]["output_directory"].as<std::string>("radar_output");
            radar_visualizer.show_distance_labels = config["radar_visualizer"]["show_distance_labels"].as<bool>(true);
            radar_visualizer.show_angle_labels = config["radar_visualizer"]["show_angle_labels"].as<bool>(true);
            radar_visualizer.update_rate_hz = config["radar_visualizer"]["update_rate_hz"].as<float>(10.0f);
        } else {
            radar_visualizer.enabled = false;
        }

        // MAVLink configuration
        if (config["mavlink"]) {
            mavlink.enabled = config["mavlink"]["enabled"].as<bool>(false);
            mavlink.serial_port = config["mavlink"]["serial_port"].as<std::string>("/dev/ttyTHS0");
            mavlink.baudrate = config["mavlink"]["baudrate"].as<int>(115200);
            mavlink.system_id = static_cast<uint8_t>(config["mavlink"]["system_id"].as<int>(1));
            mavlink.component_id = static_cast<uint8_t>(config["mavlink"]["component_id"].as<int>(158));
            mavlink.target_system_id = static_cast<uint8_t>(config["mavlink"]["target_system_id"].as<int>(1));
            mavlink.target_component_id = static_cast<uint8_t>(config["mavlink"]["target_component_id"].as<int>(1));
            mavlink.heartbeat_rate_hz = config["mavlink"]["heartbeat_rate_hz"].as<float>(15.0f);
            mavlink.obstacle_distance_rate_hz = config["mavlink"]["obstacle_distance_rate_hz"].as<float>(5.0f);
            mavlink.debug_messages = config["mavlink"]["debug_messages"].as<bool>(false);
            mavlink.min_distance = config["mavlink"]["min_distance"].as<float>(0.2f);
            mavlink.max_distance = config["mavlink"]["max_distance"].as<float>(80.0f);
            mavlink.increment_deg = config["mavlink"]["increment_deg"].as<float>(5.0f);
            mavlink.recording_channel = static_cast<uint8_t>(config["mavlink"]["recording_channel"].as<int>(16));
            mavlink.recording_pwm_threshold = static_cast<uint16_t>(config["mavlink"]["recording_pwm_threshold"].as<int>(1500));
            mavlink.recording_pwm_high = static_cast<uint16_t>(config["mavlink"]["recording_pwm_high"].as<int>(1800));
            mavlink.recording_pwm_low = static_cast<uint16_t>(config["mavlink"]["recording_pwm_low"].as<int>(1200));

            mavlink.disable_channel = static_cast<uint8_t>(config["mavlink"]["disable_channel"].as<int>(10));
            mavlink.disable_pwm_threshold = static_cast<uint16_t>(config["mavlink"]["disable_pwm_threshold"].as<int>(1500));
            mavlink.disable_pwm_high = static_cast<uint16_t>(config["mavlink"]["disable_pwm_high"].as<int>(1800));
            mavlink.disable_pwm_low = static_cast<uint16_t>(config["mavlink"]["disable_pwm_low"].as<int>(1200));
        } else {
            mavlink.enabled = false;
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error loading config file: " << e.what() << std::endl;
        return false;
    }
}

// DepthInference implementation
DepthInference::DepthInference()
    : runtime_(nullptr), engine_(nullptr), context_(nullptr),
      stream_(nullptr), gpu_input_buffer_(nullptr), gpu_output_buffer_(nullptr),
      cpu_input_buffer_(nullptr), cpu_output_buffer_(nullptr),
      input_height_(0), input_width_(0), input_channels_(3),
      output_height_(0), output_width_(0), input_size_(0), output_size_(0),
      fps_(0.0f), recording_active_(false), recording_frame_count_(0) {
}

DepthInference::~DepthInference() {
    cleanup();
}

bool DepthInference::initialize(const std::string& config_path) {
    // Load configuration
    if (!config_.loadFromFile(config_path)) {
        std::cerr << "Failed to load configuration from: " << config_path << std::endl;
        return false;
    }
    
    std::cout << "Loaded configuration from: " << config_path << std::endl;
    
    // Load TensorRT engine
    if (!loadEngine(config_.engine_path)) {
        std::cerr << "Failed to load TensorRT engine from: " << config_.engine_path << std::endl;
        return false;
    }
    
    // Setup CUDA buffers
    if (!setupBuffers()) {
        std::cerr << "Failed to setup CUDA buffers" << std::endl;
        return false;
    }
    
    // Initialize CSI camera
    if (!csi_camera_.initialize(config_.camera_sensor_id, config_.camera_width, config_.camera_height,
                               config_.camera_fps, config_.camera_sensor_mode, config_.camera_flip_method)) {
        std::cerr << "Failed to initialize CSI camera" << std::endl;
        return false;
    }

    std::cout << "CSI Camera initialized: " << csi_camera_.getFrameWidth() << "x" << csi_camera_.getFrameHeight()
              << " @ " << csi_camera_.getFramerate() << " FPS" << std::endl;
    
    // Create output directory if needed
    if (config_.save_depth_maps || config_.save_rgb) {
        createDirectoryIfNotExists(config_.output_directory);
    }

    // Initialize depth calibrator
    if (!depth_calibrator_.initialize(config_.depth_calibration)) {
        std::cerr << "Failed to initialize depth calibrator" << std::endl;
        return false;
    }

    // Initialize depth strip processor
    if (!depth_strip_processor_.initialize(config_.depth_strip)) {
        std::cerr << "Failed to initialize depth strip processor" << std::endl;
        return false;
    }

    // Initialize radar visualizer
    if (!radar_visualizer_.initialize(config_.radar_visualizer)) {
        std::cerr << "Failed to initialize radar visualizer" << std::endl;
        return false;
    }

    // Initialize MAVLink interface
    if (!mavlink_interface_.initialize(config_.mavlink)) {
        std::cerr << "Failed to initialize MAVLink interface" << std::endl;
        return false;
    }

    // Start MAVLink communication if enabled
    if (mavlink_interface_.isEnabled()) {
        if (!mavlink_interface_.start()) {
            std::cerr << "Failed to start MAVLink communication" << std::endl;
            return false;
        }
    }

    // Initialize timing
    last_time_ = std::chrono::high_resolution_clock::now();

    std::cout << "Depth inference initialized successfully!" << std::endl;
    return true;
}

bool DepthInference::loadEngine(const std::string& engine_path) {
    // Read engine file
    std::ifstream file(engine_path, std::ios::binary);
    if (!file.good()) {
        std::cerr << "Failed to read engine file: " << engine_path << std::endl;
        return false;
    }

    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::vector<char> engine_data(size);
    file.read(engine_data.data(), size);
    file.close();

    // Create TensorRT runtime
    runtime_ = createInferRuntime(logger_);
    if (!runtime_) {
        std::cerr << "Failed to create TensorRT runtime" << std::endl;
        return false;
    }

    // Deserialize engine
    engine_ = runtime_->deserializeCudaEngine(engine_data.data(), size);
    if (!engine_) {
        std::cerr << "Failed to deserialize TensorRT engine" << std::endl;
        return false;
    }

    // Create execution context
    context_ = engine_->createExecutionContext();
    if (!context_) {
        std::cerr << "Failed to create execution context" << std::endl;
        return false;
    }

    // Get input and output dimensions
#if NV_TENSORRT_MAJOR < 10
    auto input_dims = engine_->getBindingDimensions(0);
    auto output_dims = engine_->getBindingDimensions(1);
#else
    auto input_dims = engine_->getTensorShape(engine_->getIOTensorName(0));
    auto output_dims = engine_->getTensorShape(engine_->getIOTensorName(1));
#endif

    input_channels_ = input_dims.d[1];
    input_height_ = input_dims.d[2];
    input_width_ = input_dims.d[3];
    output_height_ = output_dims.d[1];
    output_width_ = output_dims.d[2];

    input_size_ = input_channels_ * input_height_ * input_width_ * sizeof(float);
    output_size_ = output_height_ * output_width_ * sizeof(float);

    std::cout << "Engine loaded successfully!" << std::endl;
    std::cout << "Input dimensions: " << input_channels_ << "x" << input_height_ << "x" << input_width_ << std::endl;
    std::cout << "Output dimensions: " << output_height_ << "x" << output_width_ << std::endl;

    return true;
}

bool DepthInference::setupBuffers() {
    // Create CUDA stream
    if (config_.use_cuda_stream) {
        if (!checkCudaError(cudaStreamCreate(&stream_), "cudaStreamCreate")) {
            return false;
        }
    }

    // Allocate GPU memory
    if (!checkCudaError(cudaMalloc(&gpu_input_buffer_, input_size_), "cudaMalloc input")) {
        return false;
    }

    if (!checkCudaError(cudaMalloc(&gpu_output_buffer_, output_size_), "cudaMalloc output")) {
        return false;
    }

    // Allocate CPU memory
    if (!checkCudaError(cudaMallocHost(&cpu_input_buffer_, input_size_), "cudaMallocHost input")) {
        return false;
    }

    if (!checkCudaError(cudaMallocHost(&cpu_output_buffer_, output_size_), "cudaMallocHost output")) {
        return false;
    }

    std::cout << "CUDA buffers allocated successfully!" << std::endl;
    return true;
}

void DepthInference::runCameraInference() {
    cv::Mat frame;
    int frame_count = 0;

    std::cout << "Starting camera inference..." << std::endl;
    std::cout << "Press 'q' to quit, 's' to save current frame" << std::endl;

    while (true) {
        // Capture frame from CSI camera
        if (!csi_camera_.captureFrame(frame)) {
            std::cerr << "Failed to capture frame from CSI camera" << std::endl;
            break;
        }

        // Run inference
        cv::Mat depth_map = inferImage(frame);

        // Apply depth calibration if enabled
        if (depth_calibrator_.isEnabled()) {
            depth_map = depth_calibrator_.calibrateDepth(depth_map);
        }

        // Process depth strip if enabled
        std::vector<PolarPoint> polar_points;
        if (depth_strip_processor_.isEnabled()) {
            polar_points = depth_strip_processor_.process(depth_map);

            // Optional: Print polar data for debugging (can be controlled by config)
            // if (config_.debug && !polar_points.empty()) {
            //     static int frame_count = 0;
            //     if (frame_count % 30 == 0) { // Print every 30 frames to avoid spam
            //         std::cout << "Frame " << frame_count << " - Polar data sample:" << std::endl;
            //         DepthStripProcessor::printPolarData(polar_points, 50);
            //     }
            //     frame_count++;
            // }

            // Update radar visualizer if enabled
            if (radar_visualizer_.isEnabled()) {
                static int radar_frame_count = 0;
                radar_visualizer_.update(polar_points, radar_frame_count++);
            }

            // Check recording control via RC channel
            if (mavlink_interface_.isEnabled() && mavlink_interface_.isConnected()) {
                bool should_record = mavlink_interface_.isRecordingActive();
                bool distance_disabled = mavlink_interface_.isDistanceDisabled();

                if (should_record && !recording_active_) {
                    startRecording();
                } else if (!should_record && recording_active_) {
                    stopRecording();
                }

                // Save frame data if recording
                if (recording_active_) {
                    saveRecordingFrame(frame, depth_map, polar_points);
                }

                // Send obstacle distance (will automatically handle disabled state)
                mavlink_interface_.sendObstacleDistanceFromPolar(polar_points);

                // Log disable state for debugging
                if (config_.debug_messages && distance_disabled) {
                    std::cout << "Distance data disabled via RC channel - sending safe values" << std::endl;
                }
            }
        }

        // Visualization
        if (config_.debug) {
            visualizeDepth(frame, depth_map);
        }

        // Save frames if enabled
        if (config_.save_depth_maps || config_.save_rgb) {
            std::string timestamp = getCurrentTimestamp();

            if (config_.save_rgb) {
                std::string rgb_path = config_.output_directory + "/rgb_" + timestamp + ".jpg";
                cv::imwrite(rgb_path, frame);
            }

            if (config_.save_depth_maps) {
                std::string depth_path = config_.output_directory + "/depth_" + timestamp;
                if (config_.depth_format == "npy") {
                    // Save as numpy array (simplified - would need proper numpy writer)
                    depth_path += ".bin";
                    std::ofstream file(depth_path, std::ios::binary);
                    file.write(reinterpret_cast<const char*>(depth_map.data),
                              depth_map.rows * depth_map.cols * sizeof(float));
                    file.close();
                } else if (config_.depth_format == "png") {
                    depth_path += ".png";
                    cv::Mat depth_8u;
                    depth_map.convertTo(depth_8u, CV_8U, 255.0 / config_.max_depth);
                    cv::imwrite(depth_path, depth_8u);
                }
            }
        }

        // Show radar display if enabled
        int radar_key = -1;
        if (radar_visualizer_.isEnabled()) {
            radar_key = radar_visualizer_.show(1);
        }

        // Handle keyboard input (from either depth visualization or radar)
        char key = cv::waitKey(1) & 0xFF;
        if (radar_key != -1) {
            key = radar_key & 0xFF;
        }

        if (key == 'q' || key == 27) { // 'q' or ESC
            break;
        } else if (key == 's') {
            std::string timestamp = getCurrentTimestamp();
            std::string rgb_path = config_.output_directory + "/saved_rgb_" + timestamp + ".jpg";
            std::string depth_path = config_.output_directory + "/saved_depth_" + timestamp + ".png";

            cv::imwrite(rgb_path, frame);
            cv::Mat depth_8u;
            depth_map.convertTo(depth_8u, CV_8U, 255.0 / config_.max_depth);
            cv::imwrite(depth_path, depth_8u);

            std::cout << "Saved frame: " << rgb_path << " and " << depth_path << std::endl;
        }

        frame_count++;
        calculateFPS();
    }

    std::cout << "Camera inference stopped. Total frames processed: " << frame_count << std::endl;
}

cv::Mat DepthInference::inferImage(const cv::Mat& image) {
    auto start_time = std::chrono::high_resolution_clock::now();

    // Preprocess image
    int original_height, original_width;
    cv::Mat preprocessed = preprocessImage(image, original_height, original_width);

    auto preprocess_end = std::chrono::high_resolution_clock::now();
    if (config_.log_preprocessing_time) {
        auto preprocess_time = std::chrono::duration<double, std::milli>(preprocess_end - start_time).count();
        logTiming("Preprocessing", preprocess_time);
    }

    // Copy preprocessed data to GPU
    std::memcpy(cpu_input_buffer_, preprocessed.data, input_size_);
    if (!checkCudaError(cudaMemcpyAsync(gpu_input_buffer_, cpu_input_buffer_, input_size_,
                                       cudaMemcpyHostToDevice, stream_), "cudaMemcpyAsync H2D")) {
        return cv::Mat();
    }

    // Run inference
    void* bindings[] = {gpu_input_buffer_, gpu_output_buffer_};
    auto inference_start = std::chrono::high_resolution_clock::now();

    bool success;
#if NV_TENSORRT_MAJOR < 10
    if (config_.use_cuda_stream) {
        success = context_->enqueueV2(bindings, stream_, nullptr);
    } else {
        success = context_->executeV2(bindings);
    }
#else
    success = context_->executeV2(bindings);
#endif

    if (!success) {
        std::cerr << "Failed to run inference" << std::endl;
        return cv::Mat();
    }

    // Copy result back to CPU
    if (!checkCudaError(cudaMemcpyAsync(cpu_output_buffer_, gpu_output_buffer_, output_size_,
                                       cudaMemcpyDeviceToHost, stream_), "cudaMemcpyAsync D2H")) {
        return cv::Mat();
    }

    if (config_.use_cuda_stream) {
        cudaStreamSynchronize(stream_);
    }

    auto inference_end = std::chrono::high_resolution_clock::now();
    if (config_.log_inference_time) {
        auto inference_time = std::chrono::duration<double, std::milli>(inference_end - inference_start).count();
        logTiming("Inference", inference_time);
    }

    // Postprocess depth map
    cv::Mat depth_map = postprocessDepth(static_cast<float*>(cpu_output_buffer_), original_height, original_width);

    auto postprocess_end = std::chrono::high_resolution_clock::now();
    if (config_.log_postprocessing_time) {
        auto postprocess_time = std::chrono::duration<double, std::milli>(postprocess_end - inference_end).count();
        logTiming("Postprocessing", postprocess_time);
    }

    return depth_map;
}

cv::Mat DepthInference::preprocessImage(const cv::Mat& image, int& original_height, int& original_width) {
    original_height = image.rows;
    original_width = image.cols;

    // Convert BGR to RGB and normalize to [0, 1]
    cv::Mat rgb_image;
    cv::cvtColor(image, rgb_image, cv::COLOR_BGR2RGB);
    rgb_image.convertTo(rgb_image, CV_32F, 1.0 / 255.0);

    // Calculate resize dimensions
    cv::Size target_size = calculateResizeSize(original_width, original_height);

    // Resize image
    cv::Mat resized_image;
    int interpolation = cv::INTER_CUBIC;
    if (config_.interpolation == "linear") interpolation = cv::INTER_LINEAR;
    else if (config_.interpolation == "nearest") interpolation = cv::INTER_NEAREST;

    cv::resize(rgb_image, resized_image, target_size, 0, 0, interpolation);

    // Pad to target size if necessary
    cv::Mat padded_image = cv::Mat::zeros(config_.input_size, config_.input_size, CV_32FC3);
    int start_y = (config_.input_size - resized_image.rows) / 2;
    int start_x = (config_.input_size - resized_image.cols) / 2;
    resized_image.copyTo(padded_image(cv::Rect(start_x, start_y, resized_image.cols, resized_image.rows)));

    // Normalize with ImageNet statistics
    std::vector<cv::Mat> channels(3);
    cv::split(padded_image, channels);

    for (int i = 0; i < 3; ++i) {
        channels[i] = (channels[i] - config_.mean[i]) / config_.std[i];
    }

    cv::merge(channels, padded_image);

    // Convert to CHW format (channels first)
    cv::Mat chw_image(1, input_channels_ * input_height_ * input_width_, CV_32F);
    float* chw_data = reinterpret_cast<float*>(chw_image.data);

    for (int c = 0; c < 3; ++c) {
        for (int h = 0; h < input_height_; ++h) {
            for (int w = 0; w < input_width_; ++w) {
                chw_data[c * input_height_ * input_width_ + h * input_width_ + w] =
                    channels[c].at<float>(h, w);
            }
        }
    }

    return chw_image;
}

cv::Mat DepthInference::postprocessDepth(const float* depth_data, int original_height, int original_width) {
    // Create depth map from output
    cv::Mat depth_map(output_height_, output_width_, CV_32F, const_cast<float*>(depth_data));

    // Resize to original image size if requested
    if (config_.output_original_size) {
        cv::Mat resized_depth;
        int interpolation = cv::INTER_LINEAR;
        if (config_.post_interpolation == "nearest") interpolation = cv::INTER_NEAREST;

        cv::resize(depth_map, resized_depth, cv::Size(original_width, original_height), 0, 0, interpolation);
        return resized_depth;
    }

    return depth_map.clone();
}

void DepthInference::visualizeDepth(const cv::Mat& rgb_image, const cv::Mat& depth_map) {
    // Create visualization window
    cv::Mat colored_depth = applyColormap(depth_map);

    // Resize images for display
    cv::Mat display_rgb, display_depth;
    cv::resize(rgb_image, display_rgb, cv::Size(config_.window_width / 2, config_.window_height));
    cv::resize(colored_depth, display_depth, cv::Size(config_.window_width / 2, config_.window_height));

    // Combine RGB and depth images side by side
    cv::Mat combined;
    cv::hconcat(display_rgb, display_depth, combined);

    // Add text overlays
    if (config_.show_fps) {
        std::string fps_text = "FPS: " + std::to_string(static_cast<int>(fps_));
        cv::putText(combined, fps_text, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 255, 0), 2);
    }

    if (config_.show_depth_range) {
        double min_depth, max_depth;
        cv::minMaxLoc(depth_map, &min_depth, &max_depth);
        std::string range_text = "Depth: " + std::to_string(min_depth) + "m - " + std::to_string(max_depth) + "m";
        cv::putText(combined, range_text, cv::Point(10, 70), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
    }

    cv::imshow("Depth Inference", combined);
}

cv::Mat DepthInference::applyColormap(const cv::Mat& depth_map) {
    // Normalize depth map to 0-255 range
    cv::Mat normalized_depth;
    cv::normalize(depth_map, normalized_depth, 0, 255, cv::NORM_MINMAX, CV_8U);

    // Apply colormap
    cv::Mat colored_depth;
    int colormap_type = cv::COLORMAP_VIRIDIS;

    if (config_.colormap == "spectral") colormap_type = cv::COLORMAP_RAINBOW;
    else if (config_.colormap == "jet") colormap_type = cv::COLORMAP_JET;
    else if (config_.colormap == "viridis") colormap_type = cv::COLORMAP_VIRIDIS;
    else if (config_.colormap == "plasma") colormap_type = cv::COLORMAP_PLASMA;

    cv::applyColorMap(normalized_depth, colored_depth, colormap_type);

    return colored_depth;
}

void DepthInference::calculateFPS() {
    auto current_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<float>(current_time - last_time_).count();
    fps_ = 1.0f / duration;
    last_time_ = current_time;
}

void DepthInference::logTiming(const std::string& operation, double time_ms) {
    std::cout << "[TIMING] " << operation << ": " << std::fixed << std::setprecision(2)
              << time_ms << " ms" << std::endl;
}

cv::Size DepthInference::calculateResizeSize(int original_width, int original_height) {
    if (!config_.keep_aspect_ratio) {
        return cv::Size(config_.input_size, config_.input_size);
    }

    float scale_width = static_cast<float>(config_.input_size) / original_width;
    float scale_height = static_cast<float>(config_.input_size) / original_height;

    float scale;
    if (config_.resize_method == "lower_bound") {
        scale = std::max(scale_width, scale_height);
    } else if (config_.resize_method == "upper_bound") {
        scale = std::min(scale_width, scale_height);
    } else { // minimal
        scale = (std::abs(1.0f - scale_width) < std::abs(1.0f - scale_height)) ? scale_width : scale_height;
    }

    int new_width = constrainToMultipleOf(static_cast<int>(scale * original_width), config_.ensure_multiple_of);
    int new_height = constrainToMultipleOf(static_cast<int>(scale * original_height), config_.ensure_multiple_of);

    // Ensure dimensions don't exceed input size
    new_width = std::min(new_width, config_.input_size);
    new_height = std::min(new_height, config_.input_size);

    return cv::Size(new_width, new_height);
}

int DepthInference::constrainToMultipleOf(int value, int multiple, int min_val, int max_val) {
    int result = static_cast<int>(std::round(static_cast<float>(value) / multiple) * multiple);

    if (max_val > 0 && result > max_val) {
        result = static_cast<int>(std::floor(static_cast<float>(value) / multiple) * multiple);
    }

    if (result < min_val) {
        result = static_cast<int>(std::ceil(static_cast<float>(value) / multiple) * multiple);
    }

    return result;
}

bool DepthInference::checkCudaError(cudaError_t error, const std::string& operation) {
    if (error != cudaSuccess) {
        std::cerr << "CUDA error in " << operation << ": " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    return true;
}

void DepthInference::cleanup() {
    // Stop recording if active
    if (recording_active_) {
        stopRecording();
    }

    // Stop MAVLink communication
    mavlink_interface_.stop();

    // Release CSI camera
    if (csi_camera_.isOpened()) {
        csi_camera_.release();
    }

    // Free CUDA memory
    if (gpu_input_buffer_) {
        cudaFree(gpu_input_buffer_);
        gpu_input_buffer_ = nullptr;
    }

    if (gpu_output_buffer_) {
        cudaFree(gpu_output_buffer_);
        gpu_output_buffer_ = nullptr;
    }

    if (cpu_input_buffer_) {
        cudaFreeHost(cpu_input_buffer_);
        cpu_input_buffer_ = nullptr;
    }

    if (cpu_output_buffer_) {
        cudaFreeHost(cpu_output_buffer_);
        cpu_output_buffer_ = nullptr;
    }

    if (stream_) {
        cudaStreamDestroy(stream_);
        stream_ = nullptr;
    }

    // Release TensorRT objects
    if (context_) {
        context_->destroy();
        context_ = nullptr;
    }

    if (engine_) {
        engine_->destroy();
        engine_ = nullptr;
    }

    if (runtime_) {
        runtime_->destroy();
        runtime_ = nullptr;
    }

    cv::destroyAllWindows();
    std::cout << "Cleanup completed" << std::endl;
}

// Utility functions
std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    ss << "_" << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

void createDirectoryIfNotExists(const std::string& path) {
    if (!std::filesystem::exists(path)) {
        std::filesystem::create_directories(path);
        std::cout << "Created directory: " << path << std::endl;
    }
}

void DepthInference::startRecording() {
    if (recording_active_) {
        return; // Already recording
    }

    // Create recording session directory with timestamp
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    char timestamp[64];
    std::strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &tm);

    recording_session_dir_ = config_.output_directory + "/recording_" + std::string(timestamp);

    // Create directories
    std::string mkdir_cmd = "mkdir -p " + recording_session_dir_ + "/frames";
    system(mkdir_cmd.c_str());
    mkdir_cmd = "mkdir -p " + recording_session_dir_ + "/depth";
    system(mkdir_cmd.c_str());
    mkdir_cmd = "mkdir -p " + recording_session_dir_ + "/polar";
    system(mkdir_cmd.c_str());

    recording_active_ = true;
    recording_frame_count_ = 0;
    recording_start_time_ = std::chrono::high_resolution_clock::now();

    std::cout << "🎥 Recording session started: " << recording_session_dir_ << std::endl;
}

void DepthInference::stopRecording() {
    if (!recording_active_) {
        return; // Not recording
    }

    recording_active_ = false;

    auto recording_duration = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::high_resolution_clock::now() - recording_start_time_).count();

    // Save session metadata
    std::string metadata_file = recording_session_dir_ + "/session_info.txt";
    std::ofstream metadata(metadata_file);
    if (metadata.is_open()) {
        metadata << "ORBIT Recording Session\n";
        metadata << "======================\n";
        metadata << "Duration: " << recording_duration << " seconds\n";
        metadata << "Total frames: " << recording_frame_count_ << "\n";
        metadata << "Average FPS: " << (recording_duration > 0 ? recording_frame_count_ / recording_duration : 0) << "\n";
        metadata.close();
    }

    std::cout << "Recording session stopped: " << recording_frame_count_
              << " frames in " << recording_duration << " seconds" << std::endl;
    std::cout << "Saved to: " << recording_session_dir_ << std::endl;
}

void DepthInference::saveRecordingFrame(const cv::Mat& frame_bgr, const cv::Mat& depth_map,
                                       const std::vector<PolarPoint>& polar_points) {
    if (!recording_active_) {
        return;
    }

    char frame_num[16];
    sprintf(frame_num, "%06d", recording_frame_count_);

    // Save original frame
    std::string frame_path = recording_session_dir_ + "/frames/frame_" + std::string(frame_num) + ".jpg";
    cv::imwrite(frame_path, frame_bgr);

    // Save depth map
    std::string depth_path = recording_session_dir_ + "/depth/depth_" + std::string(frame_num) + ".png";
    cv::Mat depth_8bit;
    depth_map.convertTo(depth_8bit, CV_8UC1, 255.0 / config_.max_depth);
    cv::imwrite(depth_path, depth_8bit);

    // Save polar data as CSV
    std::string polar_path = recording_session_dir_ + "/polar/polar_" + std::string(frame_num) + ".csv";
    std::ofstream polar_file(polar_path);
    if (polar_file.is_open()) {
        polar_file << "pixel_x,yaw_degrees,distance_meters\n";
        for (const auto& point : polar_points) {
            polar_file << point.distance << "," << point.yaw_degrees << "," << point.distance << "\n";
        }
        polar_file.close();
    }

    recording_frame_count_++;
}


