#!/bin/bash

echo "ORBIT === Omnidirectional Reactive Behavior with Integrated Tracking ==="

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "Configuring with CMake..."
cmake ..

# Check if configuration was successful
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build the project
echo "Building project..."
make -j$(nproc)

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Build completed successfully!"
    echo "Executable: ./orbit"
    echo ""
    echo "Usage:"
    echo "  ./orbit [config.yaml]"
    echo ""
    echo "Make sure you have:"
    echo "  1. TensorRT engine file at the path specified in config.yaml"
    echo "  2. CSI camera connected and accessible"
    echo "  3. Proper permissions to access camera device"
else
    echo "Build failed!"
    exit 1
fi
