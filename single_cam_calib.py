#!/usr/bin/env python3
"""
Fast Single Camera Calibration Script for Jetson Orin
Captures images first, then processes calibration at the end for better performance.

Usage:
    python3 camera_calibration.py --camera-id 0
    
Controls:
    [SPACE] - Capture image
    [P] - Process all captured images and calibrate
    [S] - Save parameters to file
    [D] - Delete last captured image
    [Q] - Quit
"""

import cv2
import numpy as np
import os
import time
import argparse
import logging
import json
import yaml
from datetime import datetime
from typing import List, Tuple, Optional
import glob

# Logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Jetson camera configuration
JETSON_CONFIG = {
    "camera_id": 0,
    "sensor_mode": 0,
    "width": 1920,
    "height": 1080,
    "framerate": 21,
    "flip_method": 0,
}

# Calibration pattern configuration
CALIBRATION_CONFIG = {
    "pattern_size": (8, 5),      # Internal corners (width, height)
    "square_size": 30.0,         # Size of each square in mm
    "min_images": 10,            # Minimum images for calibration
    "max_images": 30,            # Maximum images to collect
}

def parse_args():
    """Argument parser for camera calibration."""
    parser = argparse.ArgumentParser(description='Fast Single Camera Calibration for Jetson Orin')
    
    parser.add_argument('--camera-id', type=int, default=JETSON_CONFIG['camera_id'], 
                       help='Camera sensor ID (0 for left, 1 for right)')
    parser.add_argument('--sensor-mode', type=int, default=JETSON_CONFIG['sensor_mode'], 
                       help='Camera sensor mode')
    parser.add_argument('--width', type=int, default=JETSON_CONFIG['width'], 
                       help='Camera width')
    parser.add_argument('--height', type=int, default=JETSON_CONFIG['height'], 
                       help='Camera height')
    parser.add_argument('--framerate', type=int, default=JETSON_CONFIG['framerate'], 
                       help='Camera framerate')
    parser.add_argument('--flip-method', type=int, default=JETSON_CONFIG['flip_method'], 
                       help='Image flip method')
    
    parser.add_argument('--pattern-width', type=int, default=CALIBRATION_CONFIG['pattern_size'][0], 
                       help='Chessboard pattern width (internal corners)')
    parser.add_argument('--pattern-height', type=int, default=CALIBRATION_CONFIG['pattern_size'][1], 
                       help='Chessboard pattern height (internal corners)')
    parser.add_argument('--square-size', type=float, default=CALIBRATION_CONFIG['square_size'], 
                       help='Size of each square in mm')
    parser.add_argument('--min-images', type=int, default=CALIBRATION_CONFIG['min_images'], 
                       help='Minimum images for calibration')
    parser.add_argument('--max-images', type=int, default=CALIBRATION_CONFIG['max_images'], 
                       help='Maximum images to collect')
    
    parser.add_argument('--output-dir', type=str, default='calibration_data', 
                       help='Output directory for calibration data')
    parser.add_argument('--preview-size', type=int, default=800, 
                       help='Preview window width (height calculated from aspect ratio)')
    parser.add_argument('--debug', action='store_true', default=False, 
                       help='Enable debug output')
    
    return parser.parse_args()

class CameraOpenError(Exception):
    """Custom exception for camera open failures."""
    pass

def gstreamer_pipeline(sensor_id: int, width: int, height: int, framerate: int, 
                      sensor_mode: int, flip_method: int) -> str:
    """Generate GStreamer pipeline for Jetson camera."""
    return (
        f"nvarguscamerasrc sensor-id={sensor_id} sensor-mode={sensor_mode} ! "
        f"video/x-raw(memory:NVMM), width={width}, height={height}, format=NV12, framerate={framerate}/1 ! "
        f"nvvidconv flip-method={flip_method} ! "
        f"video/x-raw, width={width}, height={height}, format=BGRx ! "
        f"videoconvert ! "
        f"video/x-raw, format=BGR ! "
        f"queue ! "
        f"appsink drop=true sync=false"
    )

class FastCameraCalibrator:
    """Fast camera calibration class - capture first, process later."""
    
    def __init__(self, args):
        self.args = args
        self.pattern_size = (args.pattern_width, args.pattern_height)
        self.square_size = args.square_size
        self.min_images = args.min_images
        self.max_images = args.max_images
        
        # Image storage
        self.captured_images = []  # Store captured image filenames
        self.image_size = None
        
        # Calibration results
        self.camera_matrix = None
        self.dist_coeffs = None
        self.calibration_error = None
        self.is_calibrated = False
        self.valid_images = []  # Images that contain valid chessboard patterns
        self.object_points = []  # 3D points
        self.image_points = []   # 2D points
        
        # Performance tracking
        self.last_capture_time = 0
        self.capture_interval = 0.5  # Minimum time between captures
        
        # Initialize camera and setup
        self.cap = None
        self.setup_output_directory()
        self.init_camera()
        self.prepare_object_points()
        
        # Calculate preview size maintaining aspect ratio
        aspect_ratio = self.args.height / self.args.width
        self.preview_width = self.args.preview_size
        self.preview_height = int(self.preview_width * aspect_ratio)
        
    def setup_output_directory(self):
        """Create output directory if it doesn't exist."""
        if not os.path.exists(self.args.output_dir):
            os.makedirs(self.args.output_dir)
        
        # Create subdirectory for this session
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_dir = os.path.join(self.args.output_dir, f"session_cam{self.args.camera_id}_{timestamp}")
        os.makedirs(self.session_dir, exist_ok=True)
        logger.info(f"Session directory: {self.session_dir}")
            
    def init_camera(self):
        """Initialize camera with GStreamer pipeline."""
        try:
            pipeline = gstreamer_pipeline(
                self.args.camera_id, self.args.width, self.args.height,
                self.args.framerate, self.args.sensor_mode, self.args.flip_method
            )
            
            if self.args.debug:
                logger.debug(f"Camera pipeline: {pipeline}")
                
            logger.info(f"Opening camera {self.args.camera_id} at {self.args.width}x{self.args.height}@{self.args.framerate}fps")
            
            self.cap = cv2.VideoCapture(pipeline, cv2.CAP_GSTREAMER)
            
            if not self.cap.isOpened():
                raise CameraOpenError(f"Failed to open camera (ID {self.args.camera_id})")
                
            # Set image size from first frame
            ret, frame = self.cap.read()
            if ret:
                self.image_size = (frame.shape[1], frame.shape[0])  # (width, height)
                logger.info(f"Camera initialized. Image size: {self.image_size}")
            else:
                raise CameraOpenError("Failed to read first frame from camera")
                
        except Exception as e:
            logger.error(f"Camera initialization failed: {e}")
            self.cleanup()
            raise
            
    def prepare_object_points(self):
        """Prepare 3D object points for the chessboard pattern."""
        self.objp = np.zeros((self.pattern_size[0] * self.pattern_size[1], 3), np.float32)
        self.objp[:, :2] = np.mgrid[0:self.pattern_size[0], 0:self.pattern_size[1]].T.reshape(-1, 2)
        self.objp *= self.square_size  # Scale by square size in mm
        
    def capture_image(self, frame: np.ndarray) -> bool:
        """Capture and save an image."""
        current_time = time.time()
        
        # Enforce minimum interval between captures
        if current_time - self.last_capture_time < self.capture_interval:
            return False
            
        if len(self.captured_images) >= self.max_images:
            logger.warning("Maximum number of images reached")
            return False
            
        # Save image
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        filename = f"calib_{len(self.captured_images)+1:02d}_{timestamp}.jpg"
        filepath = os.path.join(self.session_dir, filename)
        
        success = cv2.imwrite(filepath, frame)
        if success:
            self.captured_images.append(filepath)
            self.last_capture_time = current_time
            logger.info(f"Captured image {len(self.captured_images)}/{self.max_images}: {filename}")
            return True
        else:
            logger.error(f"Failed to save image: {filename}")
            return False
            
    def delete_last_image(self) -> bool:
        """Delete the last captured image."""
        if not self.captured_images:
            logger.warning("No images to delete")
            return False
            
        last_image = self.captured_images.pop()
        try:
            if os.path.exists(last_image):
                os.remove(last_image)
            logger.info(f"Deleted last image. Remaining: {len(self.captured_images)}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete image: {e}")
            # Re-add to list if deletion failed
            self.captured_images.append(last_image)
            return False
            
    def process_images_and_calibrate(self) -> bool:
        """Process all captured images and perform calibration."""
        if len(self.captured_images) < self.min_images:
            logger.error(f"Need at least {self.min_images} images for calibration. Got {len(self.captured_images)}")
            return False
            
        logger.info(f"Processing {len(self.captured_images)} captured images...")
        
        # Reset calibration data
        self.object_points = []
        self.image_points = []
        self.valid_images = []
        
        # Process each captured image
        for i, image_path in enumerate(self.captured_images):
            logger.info(f"Processing image {i+1}/{len(self.captured_images)}: {os.path.basename(image_path)}")
            
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.warning(f"Failed to load image: {image_path}")
                continue
                
            # Find chessboard
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            ret, corners = cv2.findChessboardCorners(gray, self.pattern_size, None)
            
            if ret:
                # Refine corners
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                
                # Store points
                self.object_points.append(self.objp)
                self.image_points.append(corners)
                self.valid_images.append(image_path)
                
                # Save image with detected corners for verification
                corner_image = image.copy()
                cv2.drawChessboardCorners(corner_image, self.pattern_size, corners, ret)
                corner_filename = f"detected_{len(self.valid_images):02d}_{os.path.basename(image_path)}"
                corner_filepath = os.path.join(self.session_dir, corner_filename)
                cv2.imwrite(corner_filepath, corner_image)
                
                logger.info(f"  ✓ Chessboard found - corners refined and saved")
            else:
                logger.warning(f"  ✗ Chessboard not found")
                
        logger.info(f"Found valid chessboard patterns in {len(self.valid_images)}/{len(self.captured_images)} images")
        
        if len(self.valid_images) < self.min_images:
            logger.error(f"Not enough valid images for calibration. Need {self.min_images}, got {len(self.valid_images)}")
            return False
            
        # Perform calibration
        logger.info("Starting camera calibration...")
        try:
            ret, self.camera_matrix, self.dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(
                self.object_points, self.image_points, self.image_size, None, None
            )
            
            # Calculate reprojection error
            total_error = 0
            total_points = 0
            
            for i in range(len(self.object_points)):
                imgpoints2, _ = cv2.projectPoints(
                    self.object_points[i], rvecs[i], tvecs[i],
                    self.camera_matrix, self.dist_coeffs
                )
                error = cv2.norm(self.image_points[i], imgpoints2, cv2.NORM_L2) / len(imgpoints2)
                total_error += error * len(imgpoints2)
                total_points += len(imgpoints2)
                
            self.calibration_error = total_error / total_points
            self.is_calibrated = True
            
            logger.info("=" * 60)
            logger.info("CALIBRATION SUCCESSFUL!")
            logger.info("=" * 60)
            logger.info(f"Images used: {len(self.valid_images)}")
            logger.info(f"Reprojection error: {self.calibration_error:.3f} pixels")
            logger.info(f"Camera matrix:")
            logger.info(f"  fx: {self.camera_matrix[0,0]:.2f}")
            logger.info(f"  fy: {self.camera_matrix[1,1]:.2f}")
            logger.info(f"  cx: {self.camera_matrix[0,2]:.2f}")
            logger.info(f"  cy: {self.camera_matrix[1,2]:.2f}")
            logger.info(f"Distortion coefficients:")
            logger.info(f"  k1: {self.dist_coeffs[0,0]:.6f}")
            logger.info(f"  k2: {self.dist_coeffs[0,1]:.6f}")
            logger.info(f"  p1: {self.dist_coeffs[0,2]:.6f}")
            logger.info(f"  p2: {self.dist_coeffs[0,3]:.6f}")
            logger.info(f"  k3: {self.dist_coeffs[0,4]:.6f}")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"Calibration failed: {e}")
            return False
            
    def save_calibration_data(self):
        """Save calibration parameters to files."""
        if not self.is_calibrated:
            logger.error("Camera not calibrated yet. Run calibration first.")
            return False
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Prepare calibration data
        calib_data = {
            'calibration_info': {
                'timestamp': timestamp,
                'camera_id': self.args.camera_id,
                'image_size': self.image_size,
                'pattern_size': self.pattern_size,
                'square_size': self.square_size,
                'num_images_captured': len(self.captured_images),
                'num_images_valid': len(self.valid_images),
                'reprojection_error': float(self.calibration_error),
                'session_directory': self.session_dir
            },
            'camera_matrix': self.camera_matrix.tolist(),
            'distortion_coefficients': self.dist_coeffs.tolist(),
            'camera_parameters': {
                'fx': float(self.camera_matrix[0, 0]),
                'fy': float(self.camera_matrix[1, 1]),
                'cx': float(self.camera_matrix[0, 2]),
                'cy': float(self.camera_matrix[1, 2]),
                'k1': float(self.dist_coeffs[0, 0]),
                'k2': float(self.dist_coeffs[0, 1]),
                'p1': float(self.dist_coeffs[0, 2]),
                'p2': float(self.dist_coeffs[0, 3]),
                'k3': float(self.dist_coeffs[0, 4]) if len(self.dist_coeffs[0]) > 4 else 0.0
            },
            'valid_images': [os.path.basename(img) for img in self.valid_images]
        }
        
        try:
            # Save as JSON
            json_filename = f"camera_{self.args.camera_id}_calibration_{timestamp}.json"
            json_filepath = os.path.join(self.session_dir, json_filename)
            with open(json_filepath, 'w') as f:
                json.dump(calib_data, f, indent=2)
            logger.info(f"Saved calibration data to: {json_filename}")
            
            # Save as YAML
            yaml_filename = f"camera_{self.args.camera_id}_calibration_{timestamp}.yaml"
            yaml_filepath = os.path.join(self.session_dir, yaml_filename)
            with open(yaml_filepath, 'w') as f:
                yaml.dump(calib_data, f, default_flow_style=False)
            logger.info(f"Saved calibration data to: {yaml_filename}")
            
            # Save OpenCV XML format
            xml_filename = f"camera_{self.args.camera_id}_calibration_{timestamp}.xml"
            xml_filepath = os.path.join(self.session_dir, xml_filename)
            fs = cv2.FileStorage(xml_filepath, cv2.FILE_STORAGE_WRITE)
            fs.write("camera_matrix", self.camera_matrix)
            fs.write("distortion_coefficients", self.dist_coeffs)
            fs.write("image_size", np.array(self.image_size))
            fs.write("reprojection_error", self.calibration_error)
            fs.release()
            logger.info(f"Saved calibration data to: {xml_filename}")
            
            # Also save to main output directory for easy access
            main_json = os.path.join(self.args.output_dir, json_filename)
            main_xml = os.path.join(self.args.output_dir, xml_filename)
            
            with open(main_json, 'w') as f:
                json.dump(calib_data, f, indent=2)
            
            fs = cv2.FileStorage(main_xml, cv2.FILE_STORAGE_WRITE)
            fs.write("camera_matrix", self.camera_matrix)
            fs.write("distortion_coefficients", self.dist_coeffs)
            fs.write("image_size", np.array(self.image_size))
            fs.write("reprojection_error", self.calibration_error)
            fs.release()
            
            logger.info(f"Calibration files also saved to main directory: {self.args.output_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save calibration data: {e}")
            return False
            
    def draw_info(self, frame: np.ndarray) -> np.ndarray:
        """Draw information overlay on frame."""
        # Resize frame for preview
        preview_frame = cv2.resize(frame, (self.preview_width, self.preview_height))
        
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        
        # Status information
        if self.is_calibrated:
            status_color = (0, 255, 0)
            status_text = f"CALIBRATED (Error: {self.calibration_error:.3f}px)"
        else:
            status_color = (0, 165, 255)
            status_text = "READY TO CAPTURE"
        
        cv2.putText(preview_frame, f"Status: {status_text}", (10, 30), font, font_scale, status_color, thickness)
        
        # Image count
        count_color = (0, 255, 0) if len(self.captured_images) >= self.min_images else (0, 165, 255)
        cv2.putText(preview_frame, f"Captured: {len(self.captured_images)}/{self.max_images} (min: {self.min_images})", 
                   (10, 60), font, font_scale, count_color, thickness)
        
        if self.valid_images:
            cv2.putText(preview_frame, f"Valid: {len(self.valid_images)}", 
                       (10, 90), font, font_scale, (0, 255, 0), thickness)
        
        # Pattern info
        cv2.putText(preview_frame, f"Pattern: {self.pattern_size[0]}x{self.pattern_size[1]} ({self.square_size}mm)", 
                   (10, 120), font, 0.5, (255, 255, 255), 1)
        
        # Instructions
        instructions = [
            "[SPACE] Capture image",
            "[P] Process & calibrate", 
            "[S] Save parameters",
            "[D] Delete last image",
            "[Q] Quit"
        ]
        
        y_start = preview_frame.shape[0] - 120
        for i, instruction in enumerate(instructions):
            cv2.putText(preview_frame, instruction, (10, y_start + i * 22), 
                       font, 0.45, (255, 255, 255), 1)
        
        return preview_frame
        
    def run(self):
        """Main calibration loop."""
        if not self.cap or not self.cap.isOpened():
            logger.error("Camera not opened. Exiting.")
            return
            
        window_name = f"Fast Camera Calibration - Camera {self.args.camera_id}"
        cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
        
        logger.info(f"\n=== Fast Camera Calibration Started ===")
        logger.info(f"Camera: {self.args.camera_id}")
        logger.info(f"Resolution: {self.args.width}x{self.args.height}")
        logger.info(f"Pattern: {self.pattern_size[0]}x{self.pattern_size[1]} chessboard")
        logger.info(f"Square size: {self.square_size}mm")
        logger.info(f"Target images: {self.min_images}-{self.max_images}")
        logger.info(f"Session directory: {self.session_dir}")
        logger.info(f"\nControls:")
        logger.info(f"[SPACE] - Capture image (fast)")
        logger.info(f"[P] - Process all images and calibrate")
        logger.info(f"[S] - Save parameters to file")
        logger.info(f"[D] - Delete last captured image")
        logger.info(f"[Q] - Quit\n")
        
        fps_counter = 0
        fps_start_time = time.time()
        current_fps = 0
        
        while True:
            try:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("Failed to read frame from camera")
                    continue
                
                # Calculate FPS
                fps_counter += 1
                if fps_counter >= 30:
                    current_fps = fps_counter / (time.time() - fps_start_time)
                    fps_counter = 0
                    fps_start_time = time.time()
                
                # Create preview display
                display_frame = self.draw_info(frame)
                
                # Add FPS counter
                cv2.putText(display_frame, f"FPS: {current_fps:.1f}", 
                           (display_frame.shape[1] - 120, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                cv2.imshow(window_name, display_frame)
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                
                if key in [ord('q'), 27]:  # Quit
                    break
                elif key == ord(' '):  # Capture image
                    self.capture_image(frame)
                elif key == ord('p'):  # Process and calibrate
                    if len(self.captured_images) >= self.min_images:
                        logger.info("Starting image processing and calibration...")
                        self.process_images_and_calibrate()
                    else:
                        logger.warning(f"Need at least {self.min_images} images for calibration. Captured: {len(self.captured_images)}")
                elif key == ord('s'):  # Save
                    if self.is_calibrated:
                        self.save_calibration_data()
                    else:
                        logger.warning("No calibration data to save. Process images first.")
                elif key == ord('d'):  # Delete last image
                    self.delete_last_image()
                        
            except KeyboardInterrupt:
                break
                
        self.cleanup()
        
    def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up...")
        if self.cap and self.cap.isOpened():
            self.cap.release()
        cv2.destroyAllWindows()
        logger.info(f"Session data saved in: {self.session_dir}")
        logger.info("Cleanup complete.")

def main():
    args = parse_args()
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    try:
        calibrator = FastCameraCalibrator(args)
        calibrator.run()
    except (CameraOpenError, Exception) as e:
        logger.error(f"Calibration failed: {e}", exc_info=args.debug)
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main())