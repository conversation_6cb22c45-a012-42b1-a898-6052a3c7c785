#!/usr/bin/env python3
"""
Convert Depth Anything V2 PyTorch model to ONNX format for TensorRT inference.
"""

import argparse
import torch
import torch.onnx
import numpy as np
import os
from depth_anything_v2.dpt import DepthAnythingV2


def convert_to_onnx(checkpoint_path, output_path, encoder='vitl', input_height=518, input_width=518, max_depth=20.0):
    """
    Convert PyTorch model to ONNX format.
    
    Args:
        checkpoint_path (str): Path to the PyTorch checkpoint file
        output_path (str): Path to save the ONNX model
        encoder (str): Encoder type ('vits', 'vitb', 'vitl', 'vitg')
        input_size (int): Input image size (default: 518)
        max_depth (float): Maximum depth value (default: 20.0)
    """
    
    # Model configurations
    model_configs = {
        'vits': {'encoder': 'vits', 'features': 64, 'out_channels': [48, 96, 192, 384]},
        'vitb': {'encoder': 'vitb', 'features': 128, 'out_channels': [96, 192, 384, 768]},
        'vitl': {'encoder': 'vitl', 'features': 256, 'out_channels': [256, 512, 1024, 1024]},
        'vitg': {'encoder': 'vitg', 'features': 384, 'out_channels': [1536, 1536, 1536, 1536]}
    }
    
    print(f"Loading model with encoder: {encoder}")
    
    # Initialize model
    model = DepthAnythingV2(**{**model_configs[encoder], 'max_depth': max_depth})
    
    # Load checkpoint
    print(f"Loading checkpoint from: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    model.load_state_dict(checkpoint)
    
    # Set model to evaluation mode
    model.eval()
    
    # Create dummy input tensor
    # Input should be [batch_size, channels, height, width]
    # The model expects input size to be multiple of 14
    dummy_input = torch.randn(1, 3, input_height, input_width)
    
    print(f"Converting model to ONNX with input size: {input_height}x{input_width}")
    
    # Export to ONNX
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,  # Use opset 11 for better TensorRT compatibility
        do_constant_folding=True,
        input_names=['input'],
        output_names=['depth'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'depth': {0: 'batch_size'}
        },
        verbose=True
    )
    
    print(f"ONNX model saved to: {output_path}")
    
    # Verify the exported model
    print("Verifying ONNX model...")
    import onnx
    onnx_model = onnx.load(output_path)
    onnx.checker.check_model(onnx_model)
    print("ONNX model verification successful!")
    
    # Print model info
    print("\nModel Information:")
    print(f"Input shape: {dummy_input.shape}")
    
    with torch.no_grad():
        output = model(dummy_input)
        print(f"Output shape: {output.shape}")
        print(f"Output min: {output.min().item():.4f}")
        print(f"Output max: {output.max().item():.4f}")


def main():
    parser = argparse.ArgumentParser(description='Convert Depth Anything V2 to ONNX')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to PyTorch checkpoint file')
    parser.add_argument('--output', type=str, required=True,
                        help='Output ONNX file path')
    parser.add_argument('--encoder', type=str, default='vitl',
                        choices=['vits', 'vitb', 'vitl', 'vitg'],
                        help='Encoder type')
    parser.add_argument('--input-height', type=int, default=518,
                        help='Input image height')
    parser.add_argument('--input-width', type=int, default=518,
                        help='Input image width')
    parser.add_argument('--max-depth', type=float, default=20.0,
                        help='Maximum depth value')
    
    args = parser.parse_args()
    
    # Check if checkpoint exists
    if not os.path.exists(args.checkpoint):
        raise FileNotFoundError(f"Checkpoint file not found: {args.checkpoint}")
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # Convert model
    convert_to_onnx(
        checkpoint_path=args.checkpoint,
        output_path=args.output,
        encoder=args.encoder,
        input_height=args.input_height,
        input_width=args.input_width,
        max_depth=args.max_depth
    )


if __name__ == '__main__':
    main()
