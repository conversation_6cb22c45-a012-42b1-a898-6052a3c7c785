#include <iostream>
#include <cstdlib>
#include <unistd.h>
#include <cstring>
#include <fcntl.h>
#include <termios.h>
#include <chrono>
#include <thread>

// MAVLink headers
#include "mavlink/ardupilotmega/mavlink.h"

class MAVLinkCompanion {
private:
    int serial_fd = -1;
    uint8_t system_id = 1;        // Our system ID
    uint8_t component_id = 158;   // MAV_COMP_ID_PERIPHERAL (or any non-conflicting comp)

    // Autopilot discovery
    bool autopilot_detected = false;
    uint8_t autopilot_system_id = 0;
    uint8_t autopilot_component_id = 0;

    // RC state
    uint16_t rc_channels[18];
    bool rc_channels_valid = false;
    bool rc_stream_ok = false;                    // becomes true once any RC msg is received
    uint64_t last_rc_msg_time_us = 0;

    // Request management
    bool rc_request_sent = false;
    std::chrono::steady_clock::time_point last_rc_request = std::chrono::steady_clock::now();

    // App-level example: toggle recording via channel 9
    uint8_t recording_channel = 9;   // 1..18
    uint16_t recording_pwm_high = 1800;
    uint16_t recording_pwm_low  = 1200;
    bool recording_active = false;

    // --------------- Serial helpers ---------------
    bool setup_serial_port(const char* port, int baudrate) {
        serial_fd = open(port, O_RDWR | O_NOCTTY | O_NDELAY);
        if (serial_fd == -1) {
            std::cerr << "Error opening serial port: " << port << std::endl;
            return false;
        }

        struct termios options{};
        tcgetattr(serial_fd, &options);

        speed_t baud;
        switch (baudrate) {
            case 57600:  baud = B57600; break;
            case 115200: baud = B115200; break;
            case 921600: baud = B921600; break;
            default:     baud = B57600; break;
        }
        cfsetispeed(&options, baud);
        cfsetospeed(&options, baud);

        options.c_cflag &= ~PARENB;
        options.c_cflag &= ~CSTOPB;
        options.c_cflag &= ~CSIZE;
        options.c_cflag |= CS8;            // 8N1
        options.c_cflag |= CREAD | CLOCAL; // turn on READ & ignore ctrl lines

        options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
        options.c_iflag &= ~(IXON | IXOFF | IXANY);
        options.c_oflag &= ~OPOST;

        tcsetattr(serial_fd, TCSANOW, &options);
        fcntl(serial_fd, F_SETFL, FNDELAY); // non-blocking

        std::cout << "Serial port " << port << " opened at " << baudrate << " baud" << std::endl;
        return true;
    }

    static uint64_t get_time_usec() {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    void send_message(mavlink_message_t* msg) {
        uint8_t buffer[MAVLINK_MAX_PACKET_LEN];
        uint16_t len = mavlink_msg_to_send_buffer(buffer, msg);
        if (write(serial_fd, buffer, len) != (int)len) {
            std::cerr << "Error writing to serial port" << std::endl;
        }
    }

    // --------------- Stream requests ---------------
    void request_message_interval(uint16_t msgid, float rate_hz) {
        // MAV_CMD_SET_MESSAGE_INTERVAL (preferred on PX4/ArduPilot)
        mavlink_message_t msg{};
        const float interval_us = (rate_hz > 0.f) ? (1e6f / rate_hz) : -1.f; // -1 to stop
        mavlink_msg_command_long_pack(
            system_id,
            component_id,
            &msg,
            autopilot_system_id,
            autopilot_component_id,
            MAV_CMD_SET_MESSAGE_INTERVAL,
            0,
            (float)msgid,   // param1: message ID
            interval_us,    // param2: interval (us)
            0,0,0,0,0
        );
        send_message(&msg);
        std::cout << "Requested MESSAGE_INTERVAL for msg " << msgid << " at ~" << rate_hz << " Hz" << std::endl;
    }

    void request_data_stream_rc(uint16_t rate_hz) {
        // Legacy REQUEST_DATA_STREAM (still works on ArduPilot)
        mavlink_message_t msg{};
        mavlink_msg_request_data_stream_pack(
            system_id,
            component_id,
            &msg,
            autopilot_system_id,
            autopilot_component_id,
            MAV_DATA_STREAM_RC_CHANNELS,
            rate_hz,
            1 // start
        );
        send_message(&msg);
        std::cout << "Requested DATA_STREAM RC at " << rate_hz << " Hz (legacy)" << std::endl;
    }

    void ensure_rc_requests() {
        // Send (or re-send) RC stream requests every 2 seconds until we see RC
        auto now = std::chrono::steady_clock::now();
        if (!autopilot_detected) return;
        if (rc_stream_ok) return;
        if (!rc_request_sent || std::chrono::duration_cast<std::chrono::milliseconds>(now - last_rc_request).count() > 2000) {
            // Try both methods to maximize compatibility
            request_message_interval(MAVLINK_MSG_ID_RC_CHANNELS, 10.0f);
            request_message_interval(MAVLINK_MSG_ID_RC_CHANNELS_RAW, 20.0f);
            request_data_stream_rc(10);
            rc_request_sent = true;
            last_rc_request = now;
        }
    }

    // --------------- IO loop ---------------
    void read_messages_once() {
        uint8_t buffer[512];
        mavlink_message_t msg;
        mavlink_status_t status;
        int bytes_read = read(serial_fd, buffer, sizeof(buffer));
        if (bytes_read <= 0) return;
        for (int i = 0; i < bytes_read; ++i) {
            if (mavlink_parse_char(MAVLINK_COMM_0, buffer[i], &msg, &status)) {
                handle_message(&msg);
            }
        }
    }

    void handle_message(const mavlink_message_t* msg) {
        switch (msg->msgid) {
            case MAVLINK_MSG_ID_HEARTBEAT: {
                mavlink_heartbeat_t hb{};
                mavlink_msg_heartbeat_decode(msg, &hb);
                if (hb.autopilot != MAV_AUTOPILOT_INVALID) {
                    if (!autopilot_detected || autopilot_system_id != msg->sysid || autopilot_component_id != msg->compid) {
                        autopilot_detected = true;
                        autopilot_system_id = msg->sysid;
                        autopilot_component_id = msg->compid;
                        std::cout << "Autopilot detected! sys=" << (int)msg->sysid
                                  << " comp=" << (int)msg->compid
                                  << " type=" << get_vehicle_type(hb.type)
                                  << " ap=" << get_autopilot_type(hb.autopilot)
                                  << std::endl;
                    }
                }
                break;
            }
            case MAVLINK_MSG_ID_RC_CHANNELS: {
                mavlink_rc_channels_t rc{};
                mavlink_msg_rc_channels_decode(msg, &rc);
                rc_channels[0]  = rc.chan1_raw;
                rc_channels[1]  = rc.chan2_raw;
                rc_channels[2]  = rc.chan3_raw;
                rc_channels[3]  = rc.chan4_raw;
                rc_channels[4]  = rc.chan5_raw;
                rc_channels[5]  = rc.chan6_raw;
                rc_channels[6]  = rc.chan7_raw;
                rc_channels[7]  = rc.chan8_raw;
                rc_channels[8]  = rc.chan9_raw;
                rc_channels[9]  = rc.chan10_raw;
                rc_channels[10] = rc.chan11_raw;
                rc_channels[11] = rc.chan12_raw;
                rc_channels[12] = rc.chan13_raw;
                rc_channels[13] = rc.chan14_raw;
                rc_channels[14] = rc.chan15_raw;
                rc_channels[15] = rc.chan16_raw;
                rc_channels[16] = rc.chan17_raw;
                rc_channels[17] = rc.chan18_raw;
                rc_channels_valid = true;
                rc_stream_ok = true;
                last_rc_msg_time_us = get_time_usec();
                print_rc_snapshot("RC_CHANNELS");
                update_recording_state();
                break;
            }
            case MAVLINK_MSG_ID_RC_CHANNELS_RAW: {
                mavlink_rc_channels_raw_t rc{};
                mavlink_msg_rc_channels_raw_decode(msg, &rc);
                rc_channels[0] = rc.chan1_raw;
                rc_channels[1] = rc.chan2_raw;
                rc_channels[2] = rc.chan3_raw;
                rc_channels[3] = rc.chan4_raw;
                rc_channels[4] = rc.chan5_raw;
                rc_channels[5] = rc.chan6_raw;
                rc_channels[6] = rc.chan7_raw;
                rc_channels[7] = rc.chan8_raw;
                // Leave 9..18 unchanged if not provided
                rc_channels_valid = true;
                rc_stream_ok = true;
                last_rc_msg_time_us = get_time_usec();
                print_rc_snapshot("RC_CHANNELS_RAW");
                update_recording_state();
                break;
            }
            case MAVLINK_MSG_ID_COMMAND_ACK: {
                mavlink_command_ack_t ack{};
                mavlink_msg_command_ack_decode(msg, &ack);
                std::cout << "COMMAND_ACK: command=" << ack.command
                          << " result=" << (int)ack.result << std::endl;
                break;
            }
            default:
                break;
        }
    }

    void print_rc_snapshot(const char* src) {
        std::cout << src << ": ch1=" << rc_channels[0]
                  << " ch2=" << rc_channels[1]
                  << " ch3=" << rc_channels[2]
                  << " ch4=" << rc_channels[3]
                  << " ch5=" << rc_channels[4]
                  << " ch6=" << rc_channels[5]
                  << " ch7=" << rc_channels[6]
                  << " ch8=" << rc_channels[7];
        if (recording_channel >= 9 && recording_channel <= 18) {
            std::cout << " ch9=" << rc_channels[8];
        }
        std::cout << std::endl;
    }

    void update_recording_state() {
        if (!rc_channels_valid) return;
        if (recording_channel == 0 || recording_channel > 18) return;
        const uint16_t pwm = rc_channels[recording_channel - 1];
        bool prev = recording_active;
        if (pwm >= recording_pwm_high) recording_active = true;
        else if (pwm <= recording_pwm_low) recording_active = false;
        if (prev != recording_active) {
            std::cout << "Recording state changed: " << (recording_active ? "ON" : "OFF")
                      << " (ch" << (int)recording_channel << "=" << pwm << ")" << std::endl;
        }
    }

    static std::string get_vehicle_type(uint8_t type) {
        switch (type) {
            case MAV_TYPE_QUADROTOR: return "Quadcopter";
            case MAV_TYPE_HELICOPTER: return "Helicopter";
            case MAV_TYPE_FIXED_WING: return "Fixed Wing";
            case MAV_TYPE_GROUND_ROVER: return "Rover";
            case MAV_TYPE_SURFACE_BOAT: return "Boat";
            case MAV_TYPE_SUBMARINE: return "Submarine";
            case MAV_TYPE_VTOL_TILTROTOR: return "VTOL Tiltrotor";
            default: return "Unknown (" + std::to_string(type) + ")";
        }
    }

    static std::string get_autopilot_type(uint8_t autopilot) {
        switch (autopilot) {
            case MAV_AUTOPILOT_ARDUPILOTMEGA: return "ArduPilot";
            case MAV_AUTOPILOT_PX4: return "PX4";
            case MAV_AUTOPILOT_GENERIC: return "Generic";
            default: return "Unknown (" + std::to_string(autopilot) + ")";
        }
    }

public:
    bool initialize(const char* port, int baudrate) {
        for (int i = 0; i < 18; ++i) rc_channels[i] = 1500;
        rc_channels_valid = false;
        return setup_serial_port(port, baudrate);
    }

    void send_heartbeat() {
        mavlink_message_t msg{};
        mavlink_msg_heartbeat_pack(
            system_id,
            component_id,
            &msg,
            MAV_TYPE_ONBOARD_CONTROLLER,
            MAV_AUTOPILOT_INVALID,
            0, 0, 0
        );
        send_message(&msg);
    }

    void run() {
        auto last_heartbeat = std::chrono::steady_clock::now();
        auto last_info = std::chrono::steady_clock::now();

        std::cout << "MAVLink Companion running...\nPress Ctrl+C to exit" << std::endl;

        while (true) {
            read_messages_once();

            // After we see the autopilot, keep requesting RC streams until we get them
            ensure_rc_requests();

            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_heartbeat).count() >= 1000) {
                send_heartbeat();
                last_heartbeat = now;
            }

            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_info).count() >= 1000) {
                if (rc_channels_valid) {
                    std::cout << "ch" << (int)recording_channel << "="
                              << rc_channels[recording_channel - 1] << " us"
                              << (recording_active ? " [REC ON]" : " [REC OFF]")
                              << std::endl;
                } else {
                    std::cout << "Waiting for RC data..." << std::endl;
                }
                last_info = now;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    ~MAVLinkCompanion() {
        if (serial_fd != -1) close(serial_fd);
    }
};

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cout << "Usage: " << argv[0] << " <serial_port> <baudrate>\n"
                  << "Example: " << argv[0] << " /dev/ttyUSB0 57600" << std::endl;
        return 1;
    }

    const char* port = argv[1];
    int baudrate = atoi(argv[2]);

    MAVLinkCompanion companion;
    if (!companion.initialize(port, baudrate)) {
        std::cerr << "Failed to initialize companion" << std::endl;
        return 1;
    }

    try {
        companion.run();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
