<?xml version="1.0"?>
<mavlink>
  <version>3</version>
  <messages>
    <message id="17000" name="TEST_TYPES">
      <description>Test all field types</description>
      <field type="char" name="c">char</field>
      <field type="char[10]" name="s">string</field>
      <field type="uint8_t" name="u8">uint8_t</field>
      <field type="uint16_t" name="u16">uint16_t</field>
      <field print_format="0x%08x" type="uint32_t" name="u32">uint32_t</field>
      <field type="uint64_t" name="u64">uint64_t</field>
      <field type="int8_t" name="s8">int8_t</field>
      <field type="int16_t" name="s16">int16_t</field>
      <field type="int32_t" name="s32">int32_t</field>
      <field type="int64_t" name="s64">int64_t</field>
      <field type="float" name="f">float</field>
      <field type="double" name="d">double</field>
      <field type="uint8_t[3]" name="u8_array">uint8_t_array</field>
      <field type="uint16_t[3]" name="u16_array">uint16_t_array</field>
      <field type="uint32_t[3]" name="u32_array">uint32_t_array</field>
      <field type="uint64_t[3]" name="u64_array">uint64_t_array</field>
      <field type="int8_t[3]" name="s8_array">int8_t_array</field>
      <field type="int16_t[3]" name="s16_array">int16_t_array</field>
      <field type="int32_t[3]" name="s32_array">int32_t_array</field>
      <field type="int64_t[3]" name="s64_array">int64_t_array</field>
      <field type="float[3]" name="f_array">float_array</field>
      <field type="double[3]" name="d_array">double_array</field>
    </message>
  </messages>
</mavlink>
