<?xml version="1.0"?>
<mavlink>
  <!-- ardupilotmega.xml range of IDs:
    messages: 11000 - 11999
    commands: 42000 - 42999
  -->
  <include>ardupilotmega.xml</include>
  <!-- ASLUAV.xml range of IDs:
    messages: 8000 - 8999
    commands: 40001 - 41999
  -->
  <include>ASLUAV.xml</include>
  <!-- common.xml range of IDs:
    messages: 300 - 10000
    commands: 0 - 39999
    Note: entities imported from other dialects may fall outside these ranges.
  -->
  <include>common.xml</include>
  <include>development.xml</include>
  <!-- icarous.xml range of IDs:
    messages: 42000 - 42999
    commands: ? - ?
  -->
  <include>icarous.xml</include>
  <!-- matrixpilot.xml: ERROR: Duplicate message id 150 for FLEXIFUNCTION_SET (matrixpilot.xml:50) also used by SENSOR_OFFSETS (ardupilotmega.xml:1101) -->
  <!-- <include>matrixpilot.xml</include> -->
  <include>minimal.xml</include>
  <!-- paparazzi.xml: : ERROR: Duplicate message id 180 for SCRIPT_ITEM (paparazzi.xml:9) also used by CAMERA_FEEDBACK (ardupilotmega.xml:1370) -->
  <!-- <include>paparazzi.xml</include> -->
  <include>python_array_test.xml</include>
  <include>standard.xml</include>
  <include>test.xml</include>
  <include>ualberta.xml</include>
  <!-- uAvionix.xml range of IDs:
    messages: 10001-10999
    commands: ? - ?
  -->
  <include>uAvionix.xml</include>
  <!-- storm32.xml range of IDs:
    messages: 60000 - 60049
    commands: 60000 - 60049
  -->
  <include>storm32.xml</include>
  <!-- AVSSUAS.xml range of IDs:
    messages: 60050 - 60099
    commands: 60050 - 60099
  -->
  <include>AVSSUAS.xml</include>
  <!-- Herelink.xml range of IDs:
    messages: 50000 - 50099
    commands: 50000 - 50099
  -->
  <include>cubepilot.xml</include>
  <!-- ras_a.xml range of IDs:
    messages: 51000 - 51999
    commands: 51000 - 51999
    https://github.com/Dronecode/air-iop-definitions/blob/master/message_definitions/v1.0/ras_a.xml
  -->
  <!-- csAirLink.xml range of IDs:
    messages: 52000 - 52099
    commands: 52000 - 52099
  -->
  <include>csAirLink.xml</include>
  <!--Reserved range of IDs: contact <EMAIL>
    messages: 52100 - 52499
    commands: 52100 - 52499
  -->
  <!-- marsh.xml range of IDs:
    messages: 52500 - 52599
    commands: 52500 - 52599
  -->
  <include>marsh.xml</include>
  <!--Next range to allocate range of IDs:
    messages: 52600 - 52699 (< 60000)
    commands: 52600 - 52699 (< 60000)
  -->
  <enums>
    <!-- The MAV_CMD enum entries describe either: -->
    <!--  * the data payload of mission items (as used in the MISSION_ITEM_INT message) -->
    <!--  * the data payload of mavlink commands (as used in the COMMAND_INT and COMMAND_LONG messages) -->
    <!-- ALL the entries in the MAV_CMD enum have a maximum of 7 parameters -->
    <enum name="MAV_CMD">
      <description>Commands to be executed by the MAV. They can be executed on user request, or as part of a mission script. If the action is used in a mission, the parameter mapping to the waypoint/mission message is as follows: Param 1, Param 2, Param 3, Param 4, X: Param 5, Y:Param 6, Z:Param 7. This command list is similar what ARINC 424 is for commercial aircraft: A data format how to interpret waypoint/mission data. NaN and INT32_MAX may be used in float/integer params (respectively) to indicate optional/default values (e.g. to use the component's current yaw or latitude rather than a specific value). See https://mavlink.io/en/guide/xml_schema.html#MAV_CMD for information about the structure of the MAV_CMD entries</description>
      <entry value="393" name="MAV_CMD_DUMMY_ALL" hasLocation="false" isDestination="false">
        <wip/>
        <description>Dummy/temporary MAV_CMD that causes all.xml to correctly import all commands from both ardupilotmega.xml and development.xml (otherwise only one is imported, for the reasons given in https://github.com/ArduPilot/pymavlink/pull/544#discussion_r2069976980).
          It not be used, and will be removed when the toolchain is fixed.</description>
      </entry>
    </enum>
  </enums>
  <messages/>
</mavlink>
