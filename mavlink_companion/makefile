CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
INCLUDES = -I./mavlink/v2.0
LIBS = -lpthread

TARGET = mavlink_companion
SOURCES = main.cpp

# Default target
all: $(TARGET)

# Check if MAVLink headers exist
check_mavlink:
	@if [ ! -d "mavlink" ]; then \
		echo "MAVLink headers not found. Please run 'make setup' first."; \
		exit 1; \
	fi

# Build the main executable
$(TARGET): check_mavlink $(SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $(TARGET) $(SOURCES) $(LIBS)
	@echo "Build complete. Run with: ./$(TARGET) /dev/ttyUSB0 57600"

# Setup MAVLink headers
setup:
	@echo "Downloading MAVLink v2.0 headers..."
	@if [ ! -d "mavlink" ]; then \
		git clone https://github.com/mavlink/c_library_v2.git mavlink_temp && \
		mv mavlink_temp ./mavlink && \
		rm -rf mavlink_temp; \
		echo "MAVLink headers downloaded successfully."; \
	else \
		echo "MAVLink headers already exist."; \
	fi

# Clean build files
clean:
	rm -f $(TARGET)

# Clean everything including MAVLink headers
distclean: clean
	rm -rf mavlink

# Install dependencies (Ubuntu/Debian)
install_deps:
	@echo "Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y build-essential git

# Help target
help:
	@echo "Available targets:"
	@echo "  setup       - Download MAVLink headers"
	@echo "  all         - Build the companion computer application"
	@echo "  clean       - Remove build files"
	@echo "  distclean   - Remove build files and MAVLink headers"
	@echo "  install_deps- Install required dependencies (Ubuntu/Debian)"
	@echo "  help        - Show this help message"
	@echo ""
	@echo "Usage:"
	@echo "  1. make setup"
	@echo "  2. make"
	@echo "  3. ./mavlink_companion /dev/ttyUSB0 57600"

.PHONY: all setup clean distclean install_deps help check_mavlink